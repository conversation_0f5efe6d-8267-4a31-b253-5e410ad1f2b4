<?php

namespace App\Http\Requests\Api;

use App\Rules\ValidateImage;

class TableContentRequest extends BaseRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title' => 'required|min:3|max:255',
            'banner_base64' => ['nullable', new ValidateImage],
            'remove_banner' => 'nullable',
            'grade_id' => 'nullable|exists:grades,id',
            'subject_id' => 'nullable|exists:subjects,id',
            'classrooms' => "nullable|array",
            'classrooms.*' => 'nullable|integer|exists:classrooms,id',
            'start_time' => ['nullable', 'string', 'max:50'],
            'end_time' => ['nullable', 'string', 'max:50'],
            'show_answer' => 'nullable',
            'course_id' => 'nullable|exists:courses,id',
            'toc_id' => 'nullable|exists:tocs,id',
            'status' => 'nullable',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     *
     * @return array<string, string>
     */
    public function attributes(): array
    {
        return [
            'grade_id' => 'Lớp',
            'subject_id' => 'Môn',
            'banner_base64' => 'Ảnh',
            'classroom_ids' => 'Lớp học',
        ];
    }
}
