<?php

namespace App\Http\Controllers\Api;

use App\Models\Classroom;
use App\Models\ClassroomTableContent;
use App\Models\TableContent;
use App\Models\QuizResult;
use App\Models\Question;
use App\Models\User;
use App\Http\Requests\Api\TableContentRequest;
use App\Http\Resources\TableContentResource;
use App\Http\Resources\QuestionResource;
use App\Http\Resources\UserResource;
use App\Http\Resources\ClassRoomResource;
use App\Http\Resources\ClassroomTableContentResource;
use App\Services\Media\ImageService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;
use Illuminate\Support\Str;
use App\Http\Requests\Api\UpdateTitleRequest;
use App\Services\Media\FileService;

class TableContentController extends BaseController
{
    protected $imageService;
    protected $fileService;

    public function __construct(ImageService $imageService, FileService $fileService)
    {
        $this->imageService = $imageService;
        $this->fileService = $fileService;
    }

    public function quizzes(Request $request)
    {
        $page = (int) ($request->get('page', 1));
        $limit = (int) ($request->get('limit', 10));
        $tableContentsQuery = TableContent::select([
                'id',
                'title',
                'banner',
                'slug',
                'grade_id',
                'subject_id',
                'type',
                'status',
                'editor_id',
                'created_at'
            ])
            ->where('type', TableContent::TYPE_EXAM_QUIZ)
            ->when($request->q, fn($query, $value) => $query->whereLike('title', $value))
            ->when($request->grades, fn($query, $value) => $query->whereIn('grade_id', explode(',', $value)))
            ->when($request->subjects, fn($query, $value) => $query->whereIn('subject_id', explode(',', $value)))
            ->offset(($page - 1) * $limit)
            ->limit($limit)
            ->with([
                'grade:id,title',
                'subject:id,title',
                'editor:id,name',
                'questions:id,type,content,content_json,table_content_id,index'
            ]);

        if (auth()->check()) {
            $tableContentsQuery->when(filter_var($request->personal, FILTER_VALIDATE_BOOLEAN), fn($query) => $query->where('editor_id', auth()->id()))
                ->withCount([
                    'collections' => function ($query) {
                        $query->where('user_id', auth()->id());
                    }
                ]);
        }

        switch ($request->orderBy) {
            case 'latest':
                $tableContentsQuery->orderBy('id', 'DESC');
                break;
            case 'oldest':
                $tableContentsQuery->orderBy('id', 'ASC');
                break;
            case 'abc':
                $tableContentsQuery->orderBy('title', 'ASC');
                break;
            default:
                $tableContentsQuery->orderBy('id', 'DESC');
                break;
        }

        $tableContents = $tableContentsQuery->get();

        return TableContentResource::collection($tableContents);
    }

    public function getQuizBySlug($slug)
    {
        $decode = decodeSlug($slug);

        if (!$decode['id']) {
            return $this->jsonResponse('Not found!', [], 404);
        }

        $tableContent = TableContent::find($decode['id']);

        if (!$tableContent) {
            return $this->jsonResponse('Not found!', [], 404);
        }

        $tableContent->load([
                'questions:id,table_content_id,content_json,type,explain',
                'grade:id,title',
                'subject:id,title',
                'editor:id,name'
            ])
            ->loadCount('questions');

        $tableContent->timestamps = false;
        $tableContent->increment('view');
        $tableContent->timestamps = true;

        $related = TableContent::select([
                'id',
                'title',
                'slug',
                'banner',
                'grade_id',
                'subject_id',
                'editor_id',
                'view',
                'type',
                'created_at'
            ])
            ->with(['grade:id,title', 'subject:id,title'])
            ->withCount('questions')
            ->where('type', TableContent::TYPE_EXAM_QUIZ)
            ->where('subject_id', $tableContent->subject_id)
            ->orderBy('id', 'DESC')
            ->limit(8)
            ->get()
            ->where('id', '<>', $tableContent->id);

        if ($related->count() > 0) {
            $tableContent->setRelation('related', $related);
        }

        return new TableContentResource($tableContent);
    }

    public function show(TableContent $quiz)
    {
        Gate::authorize('edit', $quiz);

        $quiz->load([
            'grade:id,title',
            'subject:id,title',
            'editor:id,name',
            'questions:id,type,content,content_json,table_content_id,index'
        ])->loadCount([
            'quizResults',
            'quizResults as my_quiz_results_count' => function ($query) {
                $query->where('user_id', auth()->id());
            },
        ]);

        return new TableContentResource($quiz);
    }

    public function settingQuiz(TableContent $quiz)
    {
        $quiz->load([
            'grade:id,title',
            'subject:id,title',
            'questions:id,type,content,content_json,table_content_id,index'
        ]);

        return new TableContentResource($quiz);
    }

    public static function generateUniqueCode(): int
    {
        do {
            $code = random_int(config('web.code.min'), config('web.code.max'));
        } while (ClassroomTableContent::where('code', $code)->exists());

        return $code;
    }

    public function store(TableContentRequest $request)
    {
        DB::beginTransaction();

        try {
            $bannerPath = null;

            if ($request->banner_base64 && isBase64($request->banner_base64)) {
                $bannerPath = $this->imageService->uploadBase64(auth()->id(), $request->banner_base64);
            }

            $index = 0;

            if ($request->has('course_id')) {
                $query = TableContent::where('course_id', $request->course_id);

                if ($request->has('toc_id')) {
                    $maxIndex = $query->where('toc_id', $request->toc_id)->max('index');
                } else {
                    $maxIndex = $query->whereNull('toc_id')->max('index');
                }

                $index = $maxIndex !== null ? $maxIndex + 1 : 0;
            }

            $data = [
                'title' => $request->title,
                'subtitle' => $request->title,
                'banner' => $bannerPath,
                'code' => rand(config('web.code.min'), config('web.code.max')),
                'grade_id' => $request->grade_id,
                'subject_id' => $request->subject_id,
                'seo_title' => $request->title,
                'editor_id' => auth()->id(),
                'type' => $request->type ?? TableContent::TYPE_EXAM_QUIZ,
                'course_id' => $request->has('course_id') ? $request->course_id : null,
                'toc_id' => $request->has('toc_id') ? $request->toc_id : null,
                'index' => $index,
            ];

            $quiz = TableContent::create($data);

            if ($request->is_assign == 1) {
                $this->insertClassroomTableContent($request, $quiz);
            }

            DB::commit();

            return new TableContentResource($quiz);
        } catch (\Exception $e) {
            report($e);
            DB::rollBack();

            return $this->jsonResponse('Thêm mới thất bại!', [], 500);
        }
    }

    public function update(TableContentRequest $request, TableContent $quiz)
    {
        Gate::authorize('edit', $quiz);

        DB::beginTransaction();

        try {
            $bannerPath = $quiz->banner;

            if ($request->banner_base64 && isBase64($request->banner_base64)) {
                $bannerPath = $this->imageService->uploadBase64(auth()->id(), $request->banner_base64);
            } elseif ($request->remove_banner) {
                $bannerPath = null;
            }

            $quiz->update($request->only(
                'title',
                'grade_id',
                'subject_id',
            ) + [
                'status' => $request->status ? ($quiz->status == 1 ? 0 : 1) : $quiz->status,
                'banner' => $bannerPath,
                'seo_title' => $request->title ? $request->title : $quiz->seo_title,
            ]);

            $quiz->load(['grade:id,title', 'subject:id,title', 'editor:id,name']);

            DB::commit();

            return new TableContentResource($quiz);
        } catch (\Exception $e) {
            report($e);
            DB::rollBack();

            return $this->jsonResponse('Cập nhật thất bại!', [], 500);
        }
    }

    private function insertClassroomTableContent($request, TableContent $quiz)
    {
        $now = now();
        $data = [
            'start_time' => $request->start_time ? Carbon::parse($request->start_time)->format('Y-m-d H:i:s') : null,
            'end_time' => $request->end_time ? Carbon::parse($request->end_time)->format('Y-m-d H:i:s') : null,
            'show_answer' => $request->show_answer,
            'table_content_id' => $quiz->id,
            'created_at' => $now,
            'updated_at' => $now,
        ];

        $classroomIds = !empty($request->classrooms) ? array_unique($request->classrooms) : [null];
        $records = [];
        $codes = [];

        foreach ($classroomIds as $classroomId) {
            $code = self::generateUniqueCode();
            $codes[] = $code;
            $records[] = array_merge($data, [
                'classroom_id' => $classroomId,
                'code'         => $code,
                'author_id'    => auth()->id(),
            ]);
        }

        ClassroomTableContent::insert($records);

        return $codes;
    }

    public function assignQuiz(TableContentRequest $request, TableContent $quiz)
    {
        $codeInserted = $this->insertClassroomTableContent($request, $quiz);

        if (count($codeInserted) > 0) {
            $inserted = ClassroomTableContent::where('table_content_id', $quiz->id)
                    ->whereIn('code', $codeInserted)
                    ->get();

            $quiz->setRelation('classroomTableContents', $inserted);
        }

        return new TableContentResource($quiz);
    }

    public function destroy(TableContent $quiz)
    {
        Gate::authorize('delete', $quiz);

        if ($quiz->children()->count() > 0) {
            return $this->jsonResponse('Vui lòng xóa các mục con trước!', [], 422);
        }

        DB::transaction(function () use ($quiz) {
            QuizResult::where('table_content_id', $quiz->id)->delete();

            $quiz->delete();
        });

        return $this->jsonResponse('Xóa thành công!');
    }

    public function duplicateQuestions(TableContent $quiz, Request $request)
    {
        try {
            Gate::authorize('edit', $quiz);

            $questions = collect([]);

            if ($request->quiz_id) {
                $questions = Question::where('table_content_id', $request->quiz_id)
                    ->orderBy('id', 'asc')
                    ->get();
            }

            if ($request->question_id) {
                $question = Question::find($request->question_id);

                if ($question) {
                    $questions->push($question);
                }
            }

            if ($questions->isEmpty()) {
                return $this->jsonResponse('Không có câu hỏi nào để sao chép.', [], 422);
            }

            $maxIndex = Question::where('table_content_id', $quiz->id)
                ->max('index') ?: 0;
            $insertQueData = [];
            $newIds = [];
            $now = now();

            foreach ($questions as $index => $que) {
                $clonedQuestion = $que->replicate();

                $clonedQuestion->is_updated = Question::CLONED;
                $clonedQuestion->editor_id = auth()->id();
                $clonedQuestion->view = 0;
                $clonedQuestion->grade_id = $quiz->grade_id;
                $clonedQuestion->subject_id = $quiz->subject_id;
                $clonedQuestion->table_content_id = $quiz->id;
                $clonedQuestion->index = ++$maxIndex;
                $clonedQuestion->import_origin = 'question';
                $clonedQuestion->origin_id = $que->id;
                $clonedQuestion->created_at = $now;
                $clonedQuestion->updated_at = $now;

                $insertQueData[] = $clonedQuestion->getAttributes();
            }

            DB::transaction(function () use ($insertQueData) {
                Question::insert($insertQueData);
            });

            $newQuestions = Question::where('table_content_id', $quiz->id)
                ->orderBy('id', 'desc')
                ->take($questions->count())
                ->get()
                ->sortBy('id');

            return QuestionResource::collection($newQuestions);
        } catch (\Exception $e) {
            return $this->jsonResponse('Sao chép thất bại!', [], 500);
        }
    }

    public function getClassrooms(TableContent $quiz)
    {
        Gate::authorize('edit', $quiz);

        $classroomIds = ClassroomTableContent::select('classroom_id')
            ->where('table_content_id', $quiz->id)
            ->pluck('classroom_id')
            ->toArray();

        return $this->jsonResponse('OK', ['data' => $classroomIds], 200);
    }

    public function assignClassrooms(TableContent $quiz, Request $request)
    {
        Gate::authorize('edit', $quiz);

        $currentAssignment = $request->assignmentId ? ClassroomTableContent::find($request->assignmentId) : null;

        if (!$currentAssignment) {
            return $this->jsonResponse('Dữ liệu không hợp lệ!', [], 500);
        }

        DB::beginTransaction();

        try {
            $classroomIds = $request->input('classrooms', []);
            $validClassroomIds = Classroom::whereIn('id', $classroomIds)
                ->where('author_id', auth()->id())
                ->pluck('id')
                ->toArray();
            $currentClassroomIds = ClassroomTableContent::where('table_content_id', $quiz->id)
                ->pluck('classroom_id')
                ->toArray();

            $toDelete = array_diff($currentClassroomIds, $validClassroomIds);
            $toAdd = array_diff($validClassroomIds, $currentClassroomIds);

            if (!empty($toDelete)) {
                ClassroomTableContent::where('table_content_id', $quiz->id)
                    ->whereIn('classroom_id', $toDelete)
                    ->delete();
            }

            if (!empty($toAdd)) {
                $now = now();
                $records = [];

                foreach ($toAdd as $classroom_id) {
                    $records[] = [
                        'classroom_id' => $classroom_id,
                        'table_content_id' => $quiz->id,
                        'start_time' => $currentAssignment->start_time,
                        'end_time' => $currentAssignment->end_time,
                        'show_answer' => $currentAssignment->show_answer,
                        'code' => self::generateUniqueCode(),
                        'created_at' => $now,
                        'updated_at' => $now,
                    ];
                }

                ClassroomTableContent::insert($records);
            }

            DB::commit();

            return $this->jsonResponse('Thành công', ['data' => array_merge($toDelete, $toAdd)], 200);
        } catch (\Exception $e) {
            report($e);
            DB::rollBack();

            return $this->jsonResponse('Thất bại!', [], 500);
        }
    }

    public function quizInfo(TableContent $quiz)
    {
        $quiz->load([
            'grade:id,title',
            'subject:id,title',
            'editor:id,name',
            'questions:id,type,content,content_json,table_content_id,index'
        ])->loadCount([
            'questions',
            'quizResults',
        ]);

        if (auth()->check()) {
            $quiz->loadCount([
                'quizResults as my_quiz_results_count' => function ($query) {
                    $query->where('user_id', auth()->id());
                },
            ]);
        } else {
            $quiz->setAttribute('my_quiz_results_count', 0);
        }

        return new TableContentResource($quiz);
    }

    public function getTableContentByCode(Request $request)
    {
        if (!$request->code) {
            return $this->jsonResponse('Vui lòng nhập mã tham gia.', [], 422);
        }

        $classRoomTableContent = ClassroomTableContent::with([
            'classroom' => function ($q) {
                if (auth()->check()) {
                    $q->withCount(['users' => function ($que) {
                        $que->where('users.id', auth()->id());
                    }]);
                }}
        ])->where('code', $request->code)->whereRaw('code REGEXP "^[0-9]+$"')->first();

        if (!$classRoomTableContent) {
            return $this->jsonResponse('Không tìm thấy quiz với mã này.', [], 404);
        }

        return new ClassroomTableContentResource($classRoomTableContent);
    }

    public function getAssignmentsReport(Request $request)
    {
        $page = (int) ($request->query('page', 1));
        $limit = (int) ($request->query('limit', 10));

        $query = ClassroomTableContent::select([
            'classroom_table_content.id',
            'classroom_table_content.start_time',
            'classroom_table_content.end_time',
            'classroom_table_content.table_content_id',
            'classroom_table_content.classroom_id',
            'classroom_table_content.show_answer',
            'classroom_table_content.code',
            'table_contents.title',
            'table_contents.subtitle',
            DB::raw('COUNT(DISTINCT quiz_results.user_id) as quiz_results_count'),
            DB::raw('IF(SUM(quiz_results.total) = 0, 0, (SUM(quiz_results.total_correct) / SUM(quiz_results.total)) * 100) as correct_percentage'),
        ])
            ->join('table_contents', function ($join) {
                $join->on('table_contents.id', '=', 'classroom_table_content.table_content_id');
            })
            ->leftJoin('quiz_results', function ($join) {
                $join->on('classroom_table_content.id', '=', 'quiz_results.classroom_table_content_id')
                    ->where('quiz_results.status', QuizResult::STATUS_DONE);
            })
            ->where('classroom_table_content.author_id', auth()->id())
            ->groupBy('classroom_table_content.id');

        $total = (clone $query)->get()->count();

        $reports = $query
            ->with('classroom:id,title')
            ->orderBy('classroom_table_content.id', 'DESC')
            ->offset(($page - 1) * $limit)
            ->limit($limit)
            ->get();

        return $this->jsonResponse('Lấy dữ liệu thành công', [
            'data' => ClassroomTableContentResource::collection($reports),
            'total' => $total,
        ]);
    }

    public function getAssignmentReport(Request $request, ClassroomTableContent $assignment)
    {
        $assignment->load([
            'tableContent.questions:id,type,content,content_json,table_content_id,index',
            'classrooms',
            'classroom.users' => function($query) {
                $query->select('users.id', 'users.name', 'users.email', 'users.avatar');
            },
            'quizResults' => function($query) {
                $query->where('status', QuizResult::STATUS_DONE)
                    ->with('user:id,name,email,avatar')
                    ->orderBy('id');
            },
        ]);

        return new ClassroomTableContentResource($assignment);
    }

    public function getStudentReport(Classroom $classroom, User $student)
    {
        Gate::authorize('edit', $classroom);
        Gate::authorize('inClassRoom', [$classroom, $student]);

        $assignments = ClassroomTableContent::select([
                'classroom_table_content.id',
                'classroom_table_content.start_time',
                'classroom_table_content.end_time',
                'classroom_table_content.table_content_id',
                'classroom_table_content.classroom_id',
                'classroom_table_content.show_answer',
                'table_contents.title',
                'table_contents.subtitle',
                'table_contents.type',
                DB::raw('COUNT(quiz_results.id) as quiz_results_count'),
                DB::raw('IF(SUM(quiz_results.total) = 0, 0, (SUM(quiz_results.total_correct) / SUM(quiz_results.total)) * 100) as correct_percentage'),
                // Subquery lấy phần trăm của bản ghi cuối cùng của quiz_results sắp xếp theo id
                DB::raw("(
                    SELECT IF(qr.total = 0, 0, (qr.total_correct / qr.total) * 100)
                    FROM quiz_results as qr
                    WHERE qr.classroom_table_content_id = classroom_table_content.id
                    ORDER BY qr.id DESC
                    LIMIT 1
                ) as last_correct_percentage"),
                DB::raw("(
                    SELECT IF(qr.total = 0, 0, (qr.total_correct / qr.total) * 100)
                    FROM quiz_results as qr
                    WHERE qr.classroom_table_content_id = classroom_table_content.id
                    ORDER BY qr.id ASC
                    LIMIT 1
                ) as first_correct_percentage")
            ])
            ->join('table_contents', function ($join) {
                $join->on('table_contents.id', '=', 'classroom_table_content.table_content_id');
            })
            ->leftJoin('quiz_results', function ($join) use ($student) {
                $join->on('classroom_table_content.id', '=', 'quiz_results.classroom_table_content_id')
                    ->where('quiz_results.user_id', $student->id)
                    ->where('quiz_results.status', QuizResult::STATUS_DONE);
            })
            ->where('classroom_table_content.classroom_id', $classroom->id)
            ->groupBy('classroom_table_content.id')
            ->orderBy('classroom_table_content.start_time', 'DESC')
            ->get();

        return $this->jsonResponse('OK', [
            'data' => [
                'classroom' => new ClassRoomResource($classroom),
                'student' => new UserResource($student),
                'assignments' => ClassroomTableContentResource::collection($assignments)
            ]
        ]);
    }

    public function copyQuiz(TableContent $quiz)
    {
        try {
            DB::beginTransaction();

            $clonedQuiz = $quiz->replicate();
            $clonedQuiz->editor_id = auth()->id();
            $clonedQuiz->view = 0;
            $clonedQuiz->status = 0;
            $clonedQuiz->save();

            $questions = Question::where('table_content_id', $quiz->id)->get();

            if ($questions->isNotEmpty()) {
                $now = now();
                $questionsData = [];

                foreach ($questions as $question) {
                    $newQuestionData = $question->getAttributes();

                    unset($newQuestionData['id']);
                    $newQuestionData['is_updated'] = Question::CLONED;
                    $newQuestionData['editor_id'] = auth()->id();
                    $newQuestionData['view'] = 0;
                    $newQuestionData['table_content_id'] = $clonedQuiz->id;
                    $newQuestionData['import_origin'] = 'question';
                    $newQuestionData['origin_id'] = $question->id;
                    $newQuestionData['created_at'] = $now;
                    $newQuestionData['updated_at'] = $now;

                    $questionsData[] = $newQuestionData;
                }

                Question::insert($questionsData);
            }

            DB::commit();

            $clonedQuiz->load(['grade:id,title', 'subject:id,title', 'editor:id,name']);

            return new TableContentResource($clonedQuiz);
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);
            return $this->jsonResponse('Sao chép thất bại!', [], 500);
        }
    }

    public function updateTitle(UpdateTitleRequest $request, TableContent $quiz)
    {
        Gate::authorize('edit', $quiz);

        $quiz->title = $request->title;
        $quiz->slug = Str::slug($request->title);
        $quiz->save();

        return new TableContentResource($quiz);
    }

    public function removeFromFolder(TableContent $quiz)
    {
        Gate::authorize('edit', $quiz);

        $quiz->course_id = null;
        $quiz->toc_id = null;
        $quiz->save();

        return new TableContentResource($quiz);
    }

    public function downloadTableContent(TableContent $quiz, Request $request)
    {
        // Xác định loại file
        $type = in_array($request->type, ['quiz', 'answer', 'full']) ? $request->type : 'quiz';

        // Load câu hỏi
        $quiz->load(['questions' => function ($query) {
            $query->select(['id', 'content_json', 'table_content_id']);
        }]);

        // Tạo folder và tên file
        $folder = $this->fileService->folder('quiz/' . $quiz->id . '/');
        $fileName = $type . '.docx';
        $filePath = $folder . $fileName; // Đường dẫn trong storage
        $fileNameDownload = convert_to_slug($quiz->title) . '_' . date('Y_m_d');

        // Nếu file tồn tại, tải xuống
        if ($this->fileService->fileExists($filePath)) {
            return $this->fileService->download($filePath, $fileNameDownload);
        }

        // Tạo file mới nếu không tồn tại
        $html = '';

        try {
            if ($type == 'answer') {
                // Tạo bảng đáp án
                $html = '<table border="1" style="border-collapse: collapse; width: 100%;">';
                $html .= '<tr>';

                foreach ($quiz->questions as $i => $question) {
                    $data = $question->content_json;
                    $correctAnswer = '';

                    if (empty($data['options']) || !is_array($data['options'])) {
                        throw new \Exception("Câu hỏi {$question->id} thiếu hoặc sai định dạng options");
                    }

                    foreach ($data['options'] as $optIndex => $option) {
                        if (empty($option['content'])) {
                            throw new \Exception("Đáp án {$optIndex} của câu hỏi {$question->id} thiếu nội dung");
                        }
                        if (!empty($option['isCorrect'])) {
                            $correctAnswer .= ($correctAnswer ? ', ' : '') . chr(65 + $optIndex);
                        }
                    }

                    $html .= '<td style="padding: 8px; text-align: center;">' . ($i + 1) . '. ' . htmlspecialchars($correctAnswer) . '</td>';

                    if (($i + 1) % 10 === 0) {
                        $html .= '</tr><tr>';
                    }
                }

                $html .= '</tr>';
                $html .= '</table>';
            } else {
                // Tạo nội dung câu hỏi và đáp án
                $index = 0;
                foreach ($quiz->questions as $question) {
                    $data = $question->content_json;
                    $html .= '<p><strong>Câu ' . ($index + 1) . ':</strong></p>';
                    $html .= '<div>' . $data['content'] . '</div>';

                    if (!empty($data['options']) && is_array($data['options'])) {
                        $html .= '<ul>';
                        foreach ($data['options'] as $optIndex => $option) {
                            if (empty($option['content'])) {
                                throw new \Exception("Đáp án {$optIndex} của câu hỏi {$question->id} thiếu nội dung");
                            }
                            if ($type === 'full' && !empty($option['isCorrect'])) {
                                $html .= '<li><div custom-style="example1">' . $option['content'] . '</div></li>';
                            } else {
                                $html .= '<li>' . $option['content'] . '</li>';
                            }
                        }
                        $html .= '</ul>';
                    }

                    if (!empty($data['explain']) && $type === 'full') {
                        $html .= '<p><strong>Lời giải:</strong></p>' . $data['explain'];
                    }

                    $index++;
                }
            }

            if ($html) {
                $fileNameHtml = preg_replace('/\..+$/', '.html', $fileName);
                $fullHtml = '<!DOCTYPE html>
                <html>
                <head>
                    <meta charset="UTF-8">
                </head>
                <body>
                    <div custom-style="title">' . ($type == 'answer' ? 'Đáp án ' : '') . htmlspecialchars($quiz->title) . '</div>
                    <div custom-style="author">----------------------------------------</div>
                    ' . $html . '
                </body>
                </html>';

                // Lưu file HTML
                $savedHtmlPath = $this->fileService->saveHtmlFile($folder, $fullHtml, $fileNameHtml);

                if (!$savedHtmlPath) {
                    throw new \Exception('Không thể lưu file HTML');
                }

                // Chuyển đổi HTML sang DOCX
                $converted = $this->convertHtmlToDocxWithPandoc(
                    $this->fileService->getUrlPath($filePath), // Đường dẫn vật lý file DOCX
                    public_path('sample/custom-reference.docx') // Đường dẫn vật lý file mẫu
                );

                if ($converted) {
                    return $this->fileService->download($filePath, $fileNameDownload);
                } else {
                    throw new \Exception('Không thể chuyển đổi HTML sang DOCX hoặc file DOCX không tồn tại');
                }
            }
        } catch (\Exception $e) {
            return response()->json([
                'message' => 'Đã xảy ra lỗi trong quá trình tạo file: ' . $e->getMessage(),
            ], 500);
        }

        return response()->json([
            'message' => 'Không thể tạo file!',
        ], 500);
    }

    private function convertHtmlToDocxWithPandoc($outputPath, $referencePath = null)
    {
        try {
            $htmlFilePath = preg_replace('/\.docx$/', '.html', $outputPath);
            if (!file_exists($htmlFilePath)) {
                throw new \Exception('File HTML không tồn tại');
            }

            if ($referencePath && !file_exists($referencePath)) {
                throw new \Exception('File mẫu DOCX không tồn tại');
            }

            $command = 'pandoc ' . escapeshellarg($htmlFilePath) .
                        ' -o ' . escapeshellarg($outputPath);

            if ($referencePath) {
                $command .= ' --reference-doc=' . escapeshellarg($referencePath);
            }

            exec($command, $output, $returnVar);

            return file_exists($outputPath);
        } catch (\Exception $e) {
            return false;
        }
    }
}
