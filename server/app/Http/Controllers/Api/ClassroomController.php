<?php

namespace App\Http\Controllers\Api;

use App\Http\Requests\Api\JoinClassroomRequest;
use App\Models\ClassroomUser;
use Illuminate\Http\Request;
use App\Http\Requests\Api\AssignmentRequest;
use App\Http\Requests\Api\ClassroomRequest;
use App\Http\Requests\Api\StudentRequest;
use App\Http\Requests\Api\ImportStudentsRequest;
use App\Models\Classroom;
use App\Models\ClassroomTableContent;
use App\Models\TableContent;
use App\Models\User;
use App\Models\QuizResult;
use App\Http\Resources\ClassroomQuizResource;
use App\Http\Resources\ClassroomTableContentResource;
use App\Http\Resources\ClassRoomResource;
use App\Http\Resources\UserResource;
use App\Services\StudentExcelService;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Illuminate\Auth\Events\Registered;
use Carbon\Carbon;

class ClassroomController extends BaseController
{
    protected $studentExcelService;

    public function __construct(StudentExcelService $studentExcelService)
    {
        $this->studentExcelService = $studentExcelService;
    }

    public function index(Request $request)
    {
        $limit = $request->input('limit', 10);
        $page = $request->input('page', 1);
        $keyword = $request->input('keyword');
        $authorId = auth()->id();

        $classrooms = Classroom::query()
            ->where('author_id', $authorId)
            ->when($keyword, fn($q) => $q->whereLike('title', $keyword))
            ->with('grade')
            ->select([
                'classrooms.*',
                DB::raw('ROUND(
                    (
                        SELECT AVG(
                            (SELECT COUNT(DISTINCT quiz_results.user_id)
                            FROM quiz_results
                            WHERE quiz_results.classroom_table_content_id = classroom_table_content.id
                            AND quiz_results.status = ' . QuizResult::STATUS_DONE . ') * 100.0 /
                            (SELECT COUNT(*) FROM classroom_user WHERE classroom_user.classroom_id = classrooms.id)
                        )
                        FROM classroom_table_content
                        WHERE classroom_table_content.classroom_id = classrooms.id
                    ), 2
                ) as completion_rate')
            ])
            ->withCount([
                'classroomUsers',
                'classroomTableContents'
            ])
            ->orderBy('classrooms.grade_id', 'DESC')
            ->orderBy('id')
            ->get();

        return ClassRoomResource::collection($classrooms);
    }

    public function show(Classroom $classroom)
    {
        Gate::authorize('edit', $classroom);

        $classroom->loadCount('classroomUsers', 'classroomTableContents');

        return new ClassRoomResource($classroom);
    }

    public function store(Request $request)
    {
        $classroom = Classroom::create([
            'title' => $request->title,
            'color' => $request->color,
            'code' => rand(config('web.code.min'), config('web.code.max')),
            'grade_id' => $request->grade_id,
            'author_id' => auth()->id(),
        ]);

        $classroom->load('grade');

        return new ClassRoomResource($classroom);
    }

    public function update(ClassroomRequest $request, Classroom $classroom)
    {
        Gate::authorize('edit', $classroom);

        $classroom->update($request->only(
            'title',
            'color',
            'grade_id',
        ));

        $classroom->load('grade');

        return new ClassRoomResource($classroom);
    }

    public function destroy(Classroom $classroom)
    {
        Gate::authorize('delete', $classroom);

        $classroom->delete();

        return $this->jsonResponse('Xóa lớp học thành công!');
    }

    public function getStudents(Classroom $classroom, Request $request)
    {
        Gate::authorize('edit', $classroom);

        $keyword = $request->input('keyword');

        $students = User::select([
            'users.id',
            'users.email',
            'users.avatar',
            'users.phone',
            'users.created_at',
            'classroom_user.ident_number',
            'classroom_user.name',
            'classroom_user.temp_password',
            'classroom_user.created_at as classroom_user_created_at',
            DB::raw('count(DISTINCT quiz_results.classroom_table_content_id) AS quiz_results_count'),
        ])
            ->join('classroom_user', 'users.id', '=', 'classroom_user.user_id')
            ->leftJoin('classroom_table_content', 'classroom_table_content.classroom_id', '=', 'classroom_user.classroom_id')
            ->leftJoin('quiz_results', function ($join) {
                $join->on('classroom_table_content.id', '=', 'quiz_results.classroom_table_content_id')
                    ->on('users.id', '=', DB::raw('quiz_results.user_id'));
            })
            ->where('classroom_user.classroom_id', '=', $classroom->id)
            ->when($keyword, fn($q) => $q->whereLike('users.name', $keyword))
            ->groupByRaw('users.id, classroom_user.id')
            ->get();

        return UserResource::collection($students);
    }

    public function storeStudent(StudentRequest $request, Classroom $classroom)
    {
        $user = User::where('email', $request->email)->first();

        if (!$user) {
            if ($request->password) {
                $user = User::create([
                    'name' => $request->name,
                    'email' => $request->email,
                    'password' => Hash::make($request->password),
                    'status' => User::ACTIVE
                ]);
            } else {
                return $this->jsonResponse('Học sinh chưa có tài khoản. Vui lòng cung cấp mật khẩu để tạo tài khoản.', [], 404);
            }
        }

        $classroom->users()->attach([
            $user->id => [
                'name' => $request->name ?: $user->name,
                'ident_number' => $request->ident_number,
                'temp_password' => $request->password,
            ]
        ]);

        return new UserResource($user);
    }

    public function updateStudent(StudentRequest $request, Classroom $classroom, $studentId)
    {
        Gate::authorize('edit', $classroom);

        $classroom->users()->updateExistingPivot(
            $studentId,
            $request->only([
                'name',
                'ident_number'
            ])
        );

        $student = User::select([
            'users.id',
            'users.email',
            'users.avatar',
            'classroom_user.name',
            'classroom_user.ident_number'
        ])
            ->join('classroom_user', function ($join) use ($classroom) {
                $join->on('users.id', '=', 'classroom_user.user_id')
                    ->where('classroom_user.classroom_id', '=', $classroom->id);
            })
            ->where('users.id', $studentId)
            ->first();

        return new UserResource($student);
    }

    public function destroyStudent(Classroom $classroom, $studentId)
    {
        Gate::authorize('edit', $classroom);

        $classroom->users()->detach($studentId);

        return $this->jsonResponse('Xóa học sinh thành công!');
    }

    public function removeStudents(Request $request, Classroom $classroom)
    {
        $classroom->users()->detach($request->studentIds);

        return $this->jsonResponse('Xóa học sinh thành công!');
    }

    public function getAssignments(Request $request, Classroom $classroom)
    {
        Gate::authorize('edit', $classroom);

        $keyword = $request->input('keyword');

        $assignments = ClassroomTableContent::select([
                'classroom_table_content.id',
                'classroom_table_content.start_time',
                'classroom_table_content.end_time',
                'classroom_table_content.table_content_id',
                'classroom_table_content.show_answer',
                'table_contents.title',
                'table_contents.subtitle',
                'table_contents.type',
                DB::raw('COUNT(DISTINCT quiz_results.user_id) as quiz_results_count'),
                DB::raw('COALESCE(
                    ROUND(
                        SUM(quiz_results.total_correct) * 100.0 / NULLIF(SUM(quiz_results.total), 0),
                        1
                    ),
                    0
                ) as correct_percentage')
            ])
            ->join('table_contents', function ($join) {
                $join->on('table_contents.id', '=', 'classroom_table_content.table_content_id');
            })
            ->leftJoin('quiz_results', function ($join) {
                $join->on('classroom_table_content.id', '=', 'quiz_results.classroom_table_content_id')
                     ->where('quiz_results.status', QuizResult::STATUS_DONE);
            })
            ->where('classroom_table_content.classroom_id', $classroom->id)
            ->when($keyword, fn($q) => $q->whereLike('title', $keyword))
            ->groupBy('classroom_table_content.id')
            ->orderBy('classroom_table_content.start_time', 'DESC')
            ->get();

        return ClassroomTableContentResource::collection($assignments);
    }

    public function updateAssignment(AssignmentRequest $request, Classroom $classroom, ClassroomTableContent $assignment)
    {
        Gate::authorize('edit', $classroom);

        if ($assignment->classroom_id != $classroom->id) {
            return $this->jsonResponse('Bạn không có quyền!', [], 403);
        }

        DB::beginTransaction();

        try {
            $assignment->update([
                'start_time' => $request->start_time ? Carbon::parse($request->start_time)->format('Y-m-d H:i:s') : null,
                'end_time' => $request->end_time ? Carbon::parse($request->end_time)->format('Y-m-d H:i:s') : null,
                'show_answer' => $request->show_answer,
            ]);

            $quiz = TableContent::find($assignment->table_content_id);

            if ($quiz) {
                $data = $request->only(['title', 'description']) + ['subtitle' => $request->title];

                $quiz->fill($data);

                if ($quiz->isDirty()) {
                    $quiz->save();
                }
            }

            DB::commit();

            return $this->jsonResponse('Cập nhật thành công!', [], 200);
        } catch (\Exception $e) {
            report($e);
            DB::rollBack();

            return $this->jsonResponse('Cập nhật thất bại!', [], 500);
        }
    }

    public function destroyAssignment(Classroom $classroom, ClassroomTableContent $assignment)
    {
        Gate::authorize('edit', $classroom);

        if ($assignment->classroom_id != $classroom->id) {
            return $this->jsonResponse('Bạn không có quyền!', [], 403);
        }

        $assignment->delete();

        return $this->jsonResponse('Xóa bài tập thành công!');
    }

    public function myClassrooms(Request $request)
    {
        $limit = $request->input('limit', 10);
        $page = $request->input('page', 1);
        $keyword = $request->input('keyword');
        $authorId = auth()->id();

        $classrooms = Classroom::whereHas('classroomUsers', function ($query) use ($authorId) {
                $query->where('user_id', $authorId);
            })
            ->when($keyword, fn($q) => $q->whereLike('title', $keyword))
            ->with([
                'classroomTableContents' => function ($query) use ($authorId) {
                    $query->whereHas('tableContent', function ($q) {
                        $q->where('status', TableContent::ACTIVE);
                    })->whereDoesntHave('quizResults', function ($subQuery) use ($authorId) {
                        $subQuery->where('user_id', $authorId);
                    })
                    ->with('tableContent');
                },
                'author'
            ])
            ->withCount('classroomTableContents')
            ->orderBy('id')
            // ->offset(($page - 1) * $limit)
            // ->limit($limit)
            ->get();

        return ClassRoomResource::collection($classrooms);
    }

    public function myClassroomDetail(Classroom $classroom, Request $request)
    {
        Gate::authorize('preview', $classroom);

        $userId = auth()->id();

        $assignments = ClassroomTableContent::select([
                'classroom_table_content.id',
                'classroom_table_content.start_time',
                'classroom_table_content.end_time',
                'classroom_table_content.table_content_id',
                'table_contents.title',
                'table_contents.subtitle',
                'table_contents.type',
                'table_contents.status',
            ])
            ->join('table_contents', function ($join) {
                $join->on('table_contents.id', '=', 'classroom_table_content.table_content_id');
            })
            ->where('table_contents.status', TableContent::ACTIVE)
            ->with(['quizResults' => function ($query) use ($userId) {
                $query->where('status', QuizResult::STATUS_DONE)
                    ->where('user_id', $userId);
            }])
            ->where('classroom_table_content.classroom_id', $classroom->id)
            ->groupBy('classroom_table_content.id')
            ->orderBy('classroom_table_content.start_time', 'DESC')
            ->get();

        return ClassroomTableContentResource::collection($assignments);
    }

    public function joinClass(JoinClassroomRequest $request): \Illuminate\Http\JsonResponse
    {
        $class = Classroom::where('code', $request->class_code)->first();

        if (!$class) {
            return response()->json(['message' => 'Mã lớp không hợp lệ'], 404);
        }

        $user = auth()->user();

        if ($user) {
            // Đảm bảo không thêm trùng
            if ($class->classroomUsers()->where('user_id', $user->id)->exists()) {
                return response()->json(['message' => 'Bạn đã tham gia lớp học này rồi'], 400);
            }

            $class->users()->attach([
                $user->id => [
                    'name' => $user->name,
                ]
            ]);

            return response()->json(['message' => 'Đã tham gia lớp thành công', 'status' => 'ok']);
        }

        return response()->json(['message' => 'Có lỗi xảy ra, vui lòng thử lại!']);
    }

    public function leaveClassroom(Classroom $classroom, Request $request): \Illuminate\Http\JsonResponse
    {
        $userId = auth()->id();
        $classroomUser = ClassroomUser::where('user_id', $userId)->where('classroom_id', $classroom->id)->first();

        if (!$classroomUser) {
            return response()->json(['message' => 'Bạn không tham gia lớp này.'], 404);
        }

        $classroomUser->delete();

        return response()->json(['message' => 'Rời lớp học thành công.', 'status' => 'ok']);
    }

    public function destroyAssignmentsReport(Request $request)
    {
        $classroomTableContentIds = $request->ids;

        if (!empty($classroomTableContentIds)) {
            ClassroomTableContent::whereIn('id', $classroomTableContentIds)
                ->where('author_id', auth()->id())
                ->delete();
        }

        return $this->jsonResponse('Oke, xóa thành công', [], 200);
    }

    /**
     * Import students from an Excel file
     */
    public function importStudents(ImportStudentsRequest $request, Classroom $classroom): \Illuminate\Http\JsonResponse
    {
        Gate::authorize('edit', $classroom);

        try {
            $file = $request->file('file');
            $filePath = $file->getPathname();

            // Đọc dữ liệu từ Excel
            $result = $this->studentExcelService->readStudentsFromExcel($filePath);

            if (!empty($result['errors'])) {
                return $this->jsonResponse('File Excel có lỗi định dạng', [
                    'errors' => $result['errors']
                ], 422);
            }

            // Validate trùng lặp
            $validationErrors = $this->studentExcelService->validateStudents($result['students'], $classroom);

            if (!empty($validationErrors)) {
                return $this->jsonResponse('Dữ liệu có lỗi trùng lặp', [
                    'validation_errors' => $validationErrors,
                    'total_errors' => count($validationErrors)
                ], 422);
            }

            // Import học sinh
            $importedCount = $this->studentExcelService->importStudents($result['students'], $classroom);

            return $this->jsonResponse('Import thành công', [
                'imported_count' => $importedCount,
                'total_students' => count($result['students'])
            ]);
        } catch (\Exception $e) {
            Log::error('Import students error: ' . $e->getMessage());
            return $this->jsonResponse('Có lỗi xảy ra khi import: ' . $e->getMessage(), [], 500);
        }
    }

    /**
     * Export students to an Excel file with streaming download
     */
    public function exportStudents(Classroom $classroom): \Illuminate\Http\JsonResponse|\Symfony\Component\HttpFoundation\StreamedResponse
    {
        Gate::authorize('edit', $classroom);

        try {
            $fileName = 'danh-sach-hoc-sinh_' . str_to_slug($classroom->title) . '_' . date('d_m_Y') . '.xlsx';

            // Export danh sách học sinh với streaming
            return response()->stream(function () use ($classroom) {
                // Stream the Excel file directly without saving to disk
                $this->studentExcelService->streamExportStudents($classroom);
            }, 200, [
                'Content-Type' => 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'Content-Disposition' => 'attachment; filename="' . $fileName . '"',
                'Cache-Control' => 'no-cache, no-store, must-revalidate',
                'Pragma' => 'no-cache',
                'Expires' => '0'
            ]);
        } catch (\Exception $e) {
            Log::error('Export students error: ' . $e->getMessage());
            return $this->jsonResponse('Có lỗi xảy ra khi export: ' . $e->getMessage(), [], 500);
        }
    }
}
