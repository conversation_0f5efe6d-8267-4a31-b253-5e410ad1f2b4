<?php

namespace App\Http\Resources;

use App\Models\Role;
use Illuminate\Http\Resources\Json\JsonResource;

class UserResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array|\Illuminate\Contracts\Support\Arrayable|\JsonSerializable
     */
    public function toArray($request)
    {
        $resource = [
            'id' => $this->id,
            'name' => $this->name,
            'email' => $this->email,
            'phone' => $this->when($this->phone, $this->phone),
            'created_at' => $this->created_at,
            'joined_at' => $this->when($this->classroom_user_created_at, $this->classroom_user_created_at),
            'temp_password' => $this->when($this->temp_password, $this->temp_password),
            'avatar' => asset_image($this->avatar),
            'role' => $this->whenLoaded('roles', $this->getRoleName($this->roles)),
            'roles' => $this->whenLoaded('roles', fn() => RoleResource::collection($this->roles)),
            'ident_number' => $this->when($this->ident_number, $this->ident_number),
            'pivot' => $this->when($this->pivot, $this->pivot),
            'quiz_results_count' => $this->when($this->quiz_results_count, $this->quiz_results_count),
        ];

        return $resource;
    }

    public function getRoleName($roles): array
    {
        if (!$roles->count()) return [];

        $roleName = $roles[0]->name;

        return [
            'name' => $roleName,
            'display_name' => Role::DISPLAY_NAME[$roleName] ?? $roleName,
        ];
    }
}
