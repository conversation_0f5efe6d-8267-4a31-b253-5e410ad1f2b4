import React, { useState, useMemo } from 'react'
import Logo from '@/components/Logo'
import {
  Box,
  Typography,
  Switch,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormControl,
  FormLabel,
  Paper,
  Divider
} from '@mui/material'
import { useTheme } from '@mui/material/styles'

export default function QuizPreview({ quiz, print = false, showAnswerLabel = false }) {
  const theme = useTheme()

  // Print settings state
  const [printSettings, setPrintSettings] = useState({
    shuffleQuestions: false,
    shuffleAnswers: false,
    showAnswerKey: false,
    showInstructorName: true,
    showAnswerChoices: true,
    fontSize: 'M'
  })

  // Handle print setting changes
  const handleSettingChange = (setting) => (event) => {
    setPrintSettings(prev => ({
      ...prev,
      [setting]: event.target.checked !== undefined ? event.target.checked : event.target.value
    }))
  }

  if (!quiz) {
    return (
      <div className="d-flex justify-content-center align-items-center p-5">
        <div className="text-center text-muted fs-4">Không có dữ liệu</div>
      </div>
    )
  }

  // Process questions based on settings
  const processedQuestions = useMemo(() => {
    if (!quiz.questions) return []

    let questions = [...quiz.questions]

    // Shuffle questions if enabled
    if (printSettings.shuffleQuestions) {
      questions = questions.sort(() => Math.random() - 0.5)
    }

    // Shuffle answers within each question if enabled
    if (printSettings.shuffleAnswers) {
      questions = questions.map(question => ({
        ...question,
        content_json: {
          ...question.content_json,
          options: question.content_json?.options ?
            [...question.content_json.options].sort(() => Math.random() - 0.5) :
            question.content_json?.options
        }
      }))
    }

    return questions
  }, [quiz.questions, printSettings.shuffleQuestions, printSettings.shuffleAnswers])

  // Font size classes
  const getFontSizeClass = () => {
    switch (printSettings.fontSize) {
      case 'S': return 'font-size-small'
      case 'L': return 'font-size-large'
      case 'XL': return 'font-size-extra-large'
      default: return 'font-size-medium'
    }
  }

  return (
    <div className={`quiz-preview-container pt-0 min-vh-100 ${getFontSizeClass()}`}>
      {/* Print Settings Header */}
      {!print && (
        <Paper className="print-settings-header" elevation={1} sx={{ mb: 3, p: 3 }}>
          {/* Header with Logo and Title */}
          <Box className="d-flex align-items-center mb-3">
            <Logo width={30} height={30} />
            <Typography variant="h6" sx={{ ml: 2, fontWeight: 'bold' }}>
              Đề có thể in miễn phí
            </Typography>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Print Configuration Options */}
          <Box className="row">
            <Box className="col-md-8">
              <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
                Tùy chọn in
              </Typography>

              <Box className="row">
                <Box className="col-md-6">
                  <FormControlLabel
                    control={
                      <Switch
                        checked={printSettings.shuffleQuestions}
                        onChange={handleSettingChange('shuffleQuestions')}
                        color="primary"
                      />
                    }
                    label="Trộn thứ tự câu hỏi"
                    sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={printSettings.shuffleAnswers}
                        onChange={handleSettingChange('shuffleAnswers')}
                        color="primary"
                      />
                    }
                    label="Trộn thứ tự đáp án"
                    sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={printSettings.showAnswerKey}
                        onChange={handleSettingChange('showAnswerKey')}
                        color="primary"
                      />
                    }
                    label="Hiển thị bảng đáp án"
                    sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
                  />
                </Box>

                <Box className="col-md-6">
                  <FormControlLabel
                    control={
                      <Switch
                        checked={printSettings.showInstructorName}
                        onChange={handleSettingChange('showInstructorName')}
                        color="primary"
                      />
                    }
                    label="Hiển thị tên giáo viên"
                    sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
                  />

                  <FormControlLabel
                    control={
                      <Switch
                        checked={printSettings.showAnswerChoices}
                        onChange={handleSettingChange('showAnswerChoices')}
                        color="primary"
                      />
                    }
                    label="Hiển thị lựa chọn A, B, C, D"
                    sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
                  />
                </Box>
              </Box>
            </Box>

            {/* Font Size Selection */}
            <Box className="col-md-4">
              <FormControl component="fieldset">
                <FormLabel component="legend" sx={{ fontWeight: 'bold', mb: 1 }}>
                  Kích thước chữ
                </FormLabel>
                <RadioGroup
                  value={printSettings.fontSize}
                  onChange={handleSettingChange('fontSize')}
                  row
                >
                  <FormControlLabel value="S" control={<Radio />} label="S" />
                  <FormControlLabel value="M" control={<Radio />} label="M" />
                  <FormControlLabel value="L" control={<Radio />} label="L" />
                  <FormControlLabel value="XL" control={<Radio />} label="XL" />
                </RadioGroup>
              </FormControl>
            </Box>
          </Box>
        </Paper>
      )}

      <div className="print-header">
        <div className="header-left">
          <div className="logo-container">
            <Logo width={82} height={22} />
          </div>
          <h1 className="logo-title">{quiz.title}</h1>
        </div>
        <div className="header-right">
          {print && (
            <div className="info-line">
                <div className="info-label">Tên :</div>
                <div className="info-value"></div>
            </div>
          )}
          {printSettings.showInstructorName && quiz.editor && (
            <div className="info-line">
              <div className="info-label">GV :</div>
              <div className="info-value">{quiz.editor.name || ''}</div>
            </div>
          )}
          <div className="info-line">
            <div className="info-label">LỚP :</div>
            <div className="info-value">{print ? "" : quiz.grade?.title || ''}</div>
          </div>
          <div className="info-line">
            <div className="info-label">MÔN :</div>
            <div className="info-value">{quiz.subject?.title || ''}</div>
          </div>
          <div className="info-line">
            <div className="info-label">SỐ CÂU :</div>
            <div className="info-value">{quiz.questions_count || processedQuestions.length || 0}</div>
          </div>
        </div>
      </div>

      <div className="questions-container">
        {processedQuestions?.map((question, qIndex) => (
          <div key={qIndex} className="question-item">
            <span className="question-number">{qIndex + 1}.</span>
            <div
              className="question-content"
              dangerouslySetInnerHTML={{ __html: question.content_json?.content || '' }}
            />

            {question.content_json?.options && (
              <div className="options-container">
                {question.content_json.options.map((option, oIndex) => (
                  <div key={oIndex} className="option-item">
                    { (showAnswerLabel === true || printSettings.showAnswerChoices) && (
                      <div className="option-box">
                        {String.fromCharCode(65 + oIndex)}<span className="print-dot d-none">.</span>
                      </div>
                    )}
                    <div
                      className="box-content"
                      dangerouslySetInnerHTML={{ __html: option.content || '' }}
                    />
                  </div>
                ))}
              </div>
            )}
          </div>
        ))}
      </div>

      {/* Answer Key Section */}
      {printSettings.showAnswerKey && processedQuestions.length > 0 && (
        <div className="answer-key-section">
          <Typography variant="h6" sx={{ mt: 4, mb: 2, fontWeight: 'bold', textAlign: 'center' }}>
            BẢNG ĐÁP ÁN
          </Typography>
          <Box className="answer-key-grid">
            {processedQuestions.map((question, qIndex) => {
              const correctAnswerIndex = question.content_json?.options?.findIndex(
                option => option.is_correct
              )
              const correctAnswer = correctAnswerIndex !== -1 ?
                String.fromCharCode(65 + correctAnswerIndex) : '?'

              return (
                <Box key={qIndex} className="answer-key-item">
                  <span className="question-num">{qIndex + 1}.</span>
                  <span className="answer">{correctAnswer}</span>
                </Box>
              )
            })}
          </Box>
        </div>
      )}
    </div>
  )
}
