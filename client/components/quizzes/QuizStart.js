"use client";

import React, { useState, useEffect, useMemo } from "react";
import { useDispatch } from "react-redux";
import { useRouter, useSearchParams } from 'next/navigation';

import AppBar from '@mui/material/AppBar';
import Box from '@mui/material/Box';
import Toolbar from '@mui/material/Toolbar';
import IconButton from '@mui/material/IconButton';

import CloseIcon from '@mui/icons-material/Close';

import useSound from "@/hooks/useSound";
import { setNoti } from "@/slices/notiSlice";
import { useSelector } from "react-redux";
import toast from "react-hot-toast";

import QuizMedia from "./QuizMedia";
import RejoinLoadingDialog from "./RejoinLoadingDialog";
import Loader from '../Loader';

import { soloJoin } from '@/actions/quizResultAction';

const QuizStart = ({ initQuiz }) => {
  console.log('QuizStart');
  const [quiz, setQuiz] = useState(initQuiz);
  const dispatch = useDispatch();
  const router = useRouter();
  const searchParams = useSearchParams();
  const [open, setOpen] = React.useState(false);
  const { user } = useSelector((state) => state.auth);
  const [publish, setPublish] = useState(true);
  const [showShareInput, setShowShareInput] = useState(false);

  const isEditable = useMemo(
    () => user && quiz?.editor_id === user?.id,
    [quiz, user]
  );

  const [play, { stop }] = useSound("/assets/sounds/waiting_bg.mp3", {
    loop: true,
    onload: () => console.log('Sound loaded!'),
  });

  const handleClose = () => {
    setOpen(false);
  };

  useEffect(() => {
    play();

    return () => {
      stop();
    };
  }, [play, stop]);

  const startGame = async (gameType = 'solo') => {
    setOpen(true);

    if (quiz?.status === 0 && !isEditable) {
      setPublish(false);
      toast.error("Quiz này chưa được xuất bản. Vui lòng liên hệ với người tạo quiz để biết thêm chi tiết.");
      return;
    }

    try {
      const res = await soloJoin({
        quizId: quiz.id,
        gameType,
        assignmentId: searchParams.get('assignment'),
      });

      if (res?.data) {
        setTimeout(() => {
          router.push(`/join/game/${res.data.id}?gameType=${gameType}`);
        }, 1000);
      } else {
        throw new Error("Đã xảy ra lỗi.");
      }
    } catch (errors) {
      dispatch(setNoti(errors));
    }
  }

  const handleGoBack = () => {
    const fallback = `/quiz/${quiz.slug}-${quiz.id}`;
    const ref = document.referrer;

    if (
      ref &&
      ref !== location.href &&
      new URL(ref).origin === location.origin
    ) {
      router.back();
    } else {
      router.push(fallback);
    }
  };

  const handleShareClick = (e) => {
    setShowShareInput(true);
    handleCopyLink()
  };

  const handleCopyLink = () => {
    if (navigator?.clipboard) {
      navigator.clipboard
        .writeText(window.location.href)
        .then(() => {
          toast.success('Liên kết chia sẻ được sao chép vào clipboard.', {
            position: "bottom-center",
            icon: '👏',
            style: { fontSize: '14px', borderRadius: '20px', backgroundColor: '#9a42926e', color: '#fff' }
          });

          // Set the input value and select it
          setTimeout(() => {
            const shareInput = document.getElementById('quiz-info-share-input');
            if (shareInput) {
              shareInput.focus();
              shareInput.select();
            }
          }, 100);
        })
        .catch((err) => console.error("Lỗi copy:", err));
    } else {
      toast.error('Trình duyệt của bạn không hỗ trợ copy.');
    }
  };

  if (!quiz) return <Loader />;

  return (
    <Box sx={{ flexGrow: 1, minHeight: '100vh', backgroundColor: '#000' }}>
      <AppBar sx={{ position: 'relative', boxShadow: 'none' }} position="static" color="transparent">
        <Toolbar variant="dense">
          <IconButton
            edge="start"
            color="inherit"
            onClick={handleGoBack}
            sx={{ color: '#fff' }}
          >
            <CloseIcon fontSize="large" />
          </IconButton>
        </Toolbar>
      </AppBar>
      <div className="my-5">
        <div className="mx-auto" style={{ maxWidth: '700px' }}>
          <div className="bg-dark rounded p-4">
            <button
              className="btn btn-success2 d-block btn-lg w-100 fs-22"
              onClick={() => startGame('solo')}
            >
              <i className="bi bi-play-fill me-2 fs-3 align-middle"></i> { quiz.my_played ? 'Làm lại' : 'Bắt Đầu' }
            </button>
            <button
              className="btn btn-primary2 d-block btn-lg w-100 fs-22 mt-3"
              onClick={() => startGame('flashcard')}
            >
              <i className="bi bi-credit-card-fill me-2"></i> Flashcard
            </button>
          </div>
          <div className="bg-dark rounded p-4 mt-5">
            <QuizMedia quiz={quiz} />
            <button
              className="btn btn-light btn-sm w-100 mt-3 share-button"
              onClick={handleShareClick}
              style={{ display: showShareInput ? 'none' : 'block' }}
            >
              <i className="bi bi-share-fill me-2"></i> Chia sẻ
            </button>
            <input
              id="quiz-info-share-input"
              className="form-control mt-3 quiz-info-share-input"
              onClick={handleCopyLink}
              defaultValue={`${window.location.href}?studentShare=true`}
              readOnly
              style={{
                display: showShareInput ? 'block' : 'none',
                backgroundColor: '#f8f9fa',
                border: '1px solid #dee2e6',
                fontSize: '14px'
              }}
            />
          </div>
        </div>
        { publish && (
          <RejoinLoadingDialog open={open} onClose={handleClose} />
        )}
      </div>
    </Box>
  );
}

export default QuizStart;
