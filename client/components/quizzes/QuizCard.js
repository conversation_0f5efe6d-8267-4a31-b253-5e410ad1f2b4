import React from 'react';
import Link from "next/link";

import { styled } from "@mui/material/styles";
import Box from "@mui/material/Box";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import Avatar from "@mui/material/Avatar";
import Stack from "@mui/material/Stack";

import NextImage from "@/components/NextImage";
import Dropdown from "@/components/Dropdown";

import {
  getRandomDarkColor,
  timeElapsedString,
} from "@/utils/helpers";

const SmallAvatar = styled(Avatar)(({ theme }) => ({
  width: 40,
  height: 40,
  border: `2px solid ${theme.palette.background.paper}`,
}));

function QuizCard({
  quiz,
  actionComponent
}) {
  console.log('QuizCard');
  return (
    <Card
      className="d-flex flex-md-row flex-column"
      sx={{ margin: "20px 0", padding: "10px", overflow: "visible" }}
    >
      <CardMedia
        className="p-1 bg-body-secondary d-flex align-items-center"
        sx={{
          minWidth: 150,
        }}
      >
        <NextImage
          imgStyle={{
            objectFit: 'contain',
            objectPosition: 'center',
          }}
          src={quiz.banner}
          alt={quiz.title}
        />
      </CardMedia>
      <Box className="text-start flex-grow-1 position-relative overflow-visible">
        { actionComponent && actionComponent }
        <CardContent className="py-0 px-2">
          <Box sx={{ paddingRight: "20px", marginBottom: "15px" }}>
            <Link href={`/quiz/${quiz.slug}-${quiz.id}`} style={{ padding: "5px", textDecoration: "none", color: "inherit", display: 'block' }}>
              <span className="badge bg-light text-dark mb-2">{ quiz.type_text }</span>
              <h4 className="h5 fw-bold" style={{
                wordBreak: "break-word",
              }}>
                {quiz.title}
                {quiz.status === 0 && (
                  <span className="h6 text-danger"> [ Bản nháp ]</span>
                )}
              </h4>
              <div className="mt-2">
                <span className="text-black-50 me-3 fs-14">
                  <i className="bi bi-patch-question me-1"></i>
                  {quiz.questions_count} câu hỏi
                </span>
                {quiz.subject && (
                  <span className="text-black-50 me-3 fs-14">
                    <i className="bi bi-file-earmark-medical me-1"></i>
                    {quiz.subject.title}
                  </span>
                )}
                {quiz.grade && (
                  <span className="text-black-50 me-3 fs-14">
                    <i className="bi bi-mortarboard-fill me-1"></i>
                    {quiz.grade.title}
                  </span>
                )}
              </div>
            </Link>
          </Box>
          <Stack
            className="mt-3"
            spacing={1}
            direction="row"
            sx={{
              justifyContent: "space-between",
              alignItems: "center",
            }}
          >
            <Stack spacing={0.5} direction={{ xs: 'column', sm: 'row' }} sx={{ alignItems: "center" }}>
              <SmallAvatar
                sx={{
                  width: 30,
                  height: 30,
                  fontSize: "1rem",
                  color: "#fff",
                  background: getRandomDarkColor(0.5),
                }}
              ></SmallAvatar>
              <span className="text-black-50 ms-1 small">{quiz.editor?.name || '2048.vn'}</span>
              <span className="text-black-50 small">
                ({timeElapsedString(quiz.created_at)})
              </span>
            </Stack>
            <Stack direction="row" spacing={1}>
              <Dropdown
                renderToggle={({ onClick }) => (
                  <button
                    type="button"
                    onClick={onClick}
                    className="btn btn-sm btn-primary2 dropdown-toggle"
                    style={{
                      padding: "2px 8px",
                    }}
                  >
                    Chơi
                  </button>
                )}
                placement="bottom-end"
                renderMenu={() => (
                  <div className="dropdown-menu">
                    <Link
                      href={`/join/quiz/${quiz.id}/start`}
                      className="dropdown-item fs-14"
                    >
                      <i className="bi bi-play-circle me-2"></i>Thi thử
                    </Link>
                    { quiz.questions_count > 0 && (
                      <Link
                      href={`/dashboard/quiz/${quiz.id}/homework`}
                      className="dropdown-item fs-14"
                    >
                      <i className="bi bi-send-plus me-2"></i>Giao bài
                    </Link>
                    )}
                  </div>
                )}
              />
            </Stack>
          </Stack>
        </CardContent>
      </Box>
    </Card>
  );
}

export default React.memo(QuizCard);
