"use client";

import React from 'react';
import Logo from '@/components/Logo';
import {
  Box,
  Typography,
  Switch,
  FormControlLabel,
  RadioGroup,
  Radio,
  FormControl,
  FormLabel,
  Paper,
  Divider
} from '@mui/material';

/**
 * PrintSettingsPanel - Reusable component for quiz print settings
 * 
 * @param {Object} printSettings - Current print configuration state
 * @param {Function} onSettingsChange - Callback function to handle setting changes
 * @param {boolean} isVisible - Controls component visibility
 */
export default function PrintSettingsPanel({ 
  printSettings, 
  onSettingsChange, 
  isVisible = true 
}) {
  // Don't render if not visible
  if (!isVisible) {
    return null;
  }

  // Handle setting changes and pass to parent
  const handleSettingChange = (setting) => (event) => {
    const value = event.target.checked !== undefined ? event.target.checked : event.target.value;
    onSettingsChange(setting, value);
  };

  return (
    <Paper className="print-settings-header" elevation={1} sx={{ mb: 3, p: 3 }}>
      {/* Header with Logo and Title */}
      <Box className="d-flex align-items-center mb-3">
        <Logo width={30} height={30} />
        <Typography variant="h6" sx={{ ml: 2, fontWeight: 'bold' }}>
          Đề có thể in miễn phí
        </Typography>
      </Box>
      
      <Divider sx={{ mb: 3 }} />
      
      {/* Print Configuration Options */}
      <Box className="row">
        <Box className="col-md-8">
          <Typography variant="subtitle1" sx={{ mb: 2, fontWeight: 'bold' }}>
            Tùy chọn in
          </Typography>
          
          <Box className="row">
            <Box className="col-md-6">
              <FormControlLabel
                control={
                  <Switch
                    checked={printSettings.shuffleQuestions}
                    onChange={handleSettingChange('shuffleQuestions')}
                    color="primary"
                  />
                }
                label="Trộn thứ tự câu hỏi"
                sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={printSettings.shuffleAnswers}
                    onChange={handleSettingChange('shuffleAnswers')}
                    color="primary"
                  />
                }
                label="Trộn thứ tự đáp án"
                sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={printSettings.showAnswerKey}
                    onChange={handleSettingChange('showAnswerKey')}
                    color="primary"
                  />
                }
                label="Hiển thị bảng đáp án"
                sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
              />
            </Box>
            
            <Box className="col-md-6">
              <FormControlLabel
                control={
                  <Switch
                    checked={printSettings.showInstructorName}
                    onChange={handleSettingChange('showInstructorName')}
                    color="primary"
                  />
                }
                label="Hiển thị tên giáo viên"
                sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={printSettings.showAnswerChoices}
                    onChange={handleSettingChange('showAnswerChoices')}
                    color="primary"
                  />
                }
                label="Hiển thị lựa chọn A, B, C, D"
                sx={{ mb: 1, display: 'flex', alignItems: 'center' }}
              />
            </Box>
          </Box>
        </Box>
        
        {/* Font Size Selection */}
        <Box className="col-md-4">
          <FormControl component="fieldset">
            <FormLabel component="legend" sx={{ fontWeight: 'bold', mb: 1 }}>
              Kích thước chữ
            </FormLabel>
            <RadioGroup
              value={printSettings.fontSize}
              onChange={handleSettingChange('fontSize')}
              row
            >
              <FormControlLabel value="S" control={<Radio />} label="S" />
              <FormControlLabel value="M" control={<Radio />} label="M" />
              <FormControlLabel value="L" control={<Radio />} label="L" />
              <FormControlLabel value="XL" control={<Radio />} label="XL" />
            </RadioGroup>
          </FormControl>
        </Box>
      </Box>
    </Paper>
  );
}
