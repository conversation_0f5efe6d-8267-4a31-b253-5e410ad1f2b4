"use client";

import React, { useState, useEffect, useCallback, useMemo, useRef } from "react";
import { useRouter } from "next/navigation";
import Link from "next/link";

// Redux
import { useSelector } from "react-redux";

// Material UI Components
import { useTheme } from "@mui/material/styles";
import useMediaQuery from "@mui/material/useMediaQuery";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardMedia from "@mui/material/CardMedia";
import Avatar from "@mui/material/Avatar";
import Stack from "@mui/material/Stack";
import Button from "@mui/material/Button";
import Box from "@mui/material/Box";
import SwipeableDrawer from "@mui/material/SwipeableDrawer";
import CircularProgress from "@mui/material/CircularProgress";
import Container from "@mui/material/Container";
import EditIcon from "@mui/icons-material/Edit";

// Third-party Libraries
import { QRCodeCanvas } from "qrcode.react";
import toast from "react-hot-toast";

// Custom Components
import Dropdown from "@/components/Dropdown";
import NextImage from "@/components/NextImage";
import EditableText from "../EditableText";
import DialogQuizForm from "./DialogQuizForm";
import DialogDragDropCropImage from "../DragDropCropImage";
import QuizPreviewModal from "./QuizPreviewModal";
import SaveToCollectionModal from "./SaveToCollectionModal";
import QrDownload from "./QrDownload";

// Actions & Contexts
import { updateQuiz, removeQuiz, copyQuiz } from "@/actions/quizAction";
import { useConfirm } from "@/contexts/ConfirmContext";

// Utilities
import localStorageHelper from "@/utils/localStorageHelper";
import { isBase64, convertToSlug, getDayCurrent } from "@/utils/helpers";
import axios from "axios";

const ActionButtons = ({ quiz }) => {
  const router = useRouter();
  const theme = useTheme();
  const matchDownMD = useMediaQuery(theme.breakpoints.down("md"));
  const isMobile = useMediaQuery(theme.breakpoints.down("sm"));
  const isTablet = useMediaQuery('(max-width: 1280px)');
  const confirmDelete = useConfirm();
  const qrContainerRef = useRef(null);
  const sentinelRef = useRef(null);
  const questionCount = useMemo(
    () => quiz?.questions_count || quiz?.questions?.length || 0,
    [quiz]
  );

  const [isStickyActive, setIsStickyActive] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [openSaveModal, setOpenSaveModal] = useState(false);
  const [openDrawer, setOpenDrawer] = useState(false);
  const [openPrintModal, setOpenPrintModal] = useState(false);
  const [openDialogQuizFrom, setOpenDialogQuizFrom] = useState(false);
  const [openDialogCropImage, setOpenDialogCropImage] = useState(false);
  const [mounted, setMounted] = useState(false);

  const { user } = useSelector((state) => state.auth);
  const isEditable = useMemo(
    () => user && quiz?.editor_id === user?.id,
    [quiz, user]
  );
  const isOwner = useMemo(() => quiz?.editor_id == user?.id, [quiz, user]);

  useEffect(() => {
    if (!quiz?.id) return;

    const shouldRefresh = localStorageHelper.get("quiz_refresh") === quiz.id;

    if (shouldRefresh) {
      localStorageHelper.remove("quiz_refresh");
      router.refresh();
    }
  }, [quiz?.id]);

  useEffect(() => {
    if (isMobile) return;

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsStickyActive(!entry.isIntersecting);
      },
      { threshold: 0 }
    );

    if (sentinelRef.current) observer.observe(sentinelRef.current);

    return () => {
      if (sentinelRef.current) observer.unobserve(sentinelRef.current);
    };
  }, [isMobile]);

  useEffect(() => {
    setMounted(true);
  }, []);

  const handleOpenDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(true);
  }, []);

  const handleCloseDialogQuizFrom = useCallback(() => {
    setOpenDialogQuizFrom(false);
  }, []);

  const handleOpenDialogCropImage = useCallback(() => {
    setOpenDialogCropImage(true);
  }, []);

  const handleCloseDialogCropImage = useCallback(() => {
    setOpenDialogCropImage(false);
  }, []);

  const handleOpenPrintModal = useCallback(() => {
    setOpenPrintModal(true);
  }, []);

  const handleClosePrintModal = useCallback(() => {
    setOpenPrintModal(false);
  }, []);

  const handleOpenSaveModal = useCallback(() => setOpenSaveModal(true), []);
  const handleCloseSaveModal = useCallback(() => setOpenSaveModal(false), []);

  const editQuiz = useCallback(
    async (data, callback) => {
      try {
        if (data.title) {
          data.slug = data.title;
        }

        const { data: newQuiz } = await updateQuiz({ ...quiz, ...data });

        if (newQuiz) {
          await fetch(
            `/api/revalidate?path=${encodeURIComponent(
              `/quiz/${newQuiz.slug}-${newQuiz.id}`
            )}&path=${encodeURIComponent(`/quiz/${quiz.slug}-${quiz.id}`)}`
          );

          toast.success("Sửa Quiz thành công");

          if (typeof callback === "function") {
            callback();
          }

          if (newQuiz.slug != quiz.slug) {
            router.push(`/quiz/${newQuiz.slug}-${newQuiz.id}`);
          } else {
            router.refresh();
          }

          return;
        } else {
          throw new Error("Đã xảy ra lỗi. Không thể cập nhật quiz");
        }
      } catch (error) {
        console.error(error);
        toast.error("Đã xảy ra lỗi. Vui lòng thử lại");
      }
    },
    [quiz, router]
  );

  const deleteQuiz = useCallback(async () => {
    const ans = await confirmDelete(
      "Xóa Quizz?",
      "Bạn có chắc chắn muốn xóa quiz này không?"
    );

    if (ans) {
      try {
        const res = await removeQuiz(quiz.id);

        if (res) {
          toast.success("Xóa thành công");
          router.push("/dashboard/my-library");
        }
      } catch (error) {
        console.error(error);
        toast.error("Đã xảy ra lỗi. Vui lòng thử lại");
      }
    }
  }, [quiz.id, confirmDelete, router]);

  const handleCopyQuiz = async () => {
    if (isLoading) return;

    if (quiz?.status === 0) {
      toast.error("Quiz này chưa được xuất bản. Vui lòng liên hệ với người tạo quiz để biết thêm chi tiết.");
      return;
    }

    setIsLoading(true);

    try {
      const { data: newQuiz } = await copyQuiz(quiz.id);
      toast.success("Sao chép quiz thành công");
      router.push(`/dashboard/quiz/${newQuiz.id}/edit`);
    } catch (error) {
      console.error(error);
      toast.error("Không thể sao chép quiz. Vui lòng thử lại sau.");
      setIsLoading(false);
    }
  };

  const handleSaveQuiz = () => {
    if (user) {
      handleOpenSaveModal();
    } else {
      router.push(`/login?backUrl=${encodeURIComponent(window?.location.href)}`);
    }
  };

  const handleCopyLink = () => {
    navigator.clipboard
      .writeText(window.location.href)
      .then(() => toast.success(`Copy đường dẫn thành công`))
      .catch((err) => console.error("Lỗi copy:", err));
  };

  const downloadQuiz = async (type = 'full') => {
    if (quiz?.status === 0 || questionCount <= 0) {
      toast.error("Quiz này chưa được xuất bản. Vui lòng liên hệ với người tạo quiz để biết thêm chi tiết.");
      return;
    }

    try {
      //Do không truyền được blob tại Action mới phải gọi axios trực tiếp tại đây
      const response = await axios.get(`${process.env.NEXT_PUBLIC_BACKEND_URL}/api/quiz/${quiz.id}/download?type=${type}`,{ responseType: 'blob'});
      const disposition = response.headers['content-disposition'];
      let filename = `${ convertToSlug(quiz.title) }-${ getDayCurrent() }.docx`;
      if (disposition && disposition.indexOf('attachment') !== -1) {
          const matches = /filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/.exec(disposition);
          if (matches != null && matches[1]) {
              filename = decodeURIComponent(matches[1].replace(/['"]/g, ''));
          }
      }

      // Tạo URL từ blob và kích hoạt tải xuống
      const url = window.URL.createObjectURL(new Blob([response.data]));
      const link = document.createElement('a');
      link.href = url;
      link.setAttribute('download', filename);
      document.body.appendChild(link);
      link.click();
      link.remove();
      window.URL.revokeObjectURL(url);
    } catch (error) {
      toast.error("Có lỗi xảy ra khi tải Quiz");
      console.error('Error downloading quiz:', error.message);
    }
  };

  const stickyStyle = isMobile
    ? {
        position: "fixed",
        bottom: 0,
        left: 0,
        right: 0,
        padding: "16px 16px 24px",
        zIndex: 1000,
        background: "#fff",
        borderTop: "1px solid #09090933",
      }
    : {
        position: "sticky",
        top: 68,
        padding: "10px",
        paddingBottom: 0,
        zIndex: 100,
        display: "flex",
        alignItems: "center",
        gap: "30px",
        background: isStickyActive ? "#fff" : "#eef2f6",
        border: isStickyActive ? "1px solid #09090933" : "none",
        boxShadow: isStickyActive
          ? "rgba(99, 99, 99, 0.2) 0px 2px 8px 0px"
          : "none",
        transition: "all 0.15s ease",
      };

  const renderPrimaryBtnGroup = () => (
    <>
      {user && questionCount > 0 && (
        <Link
          href={`/dashboard/quiz/${quiz.id}/homework`}
          className="btn btn-primary4"
          style={{ boxShadow: "0 4px #d1c4e9" }}
        >
          <i className="bi bi-send me-2"></i>
          <span>Giao bài</span>
        </Link>
      )}
      <Link
        href={`/join/quiz/${quiz.id}/start`}
        className="btn btn-primary2"
        style={{ boxShadow: "0 4px #5d08a8" }}
      >
        <i className="bi bi-play-circle me-2"></i>
        <span>Bắt đầu ngay</span>
      </Link>
    </>
  );

  const renderShareDropdown = (isMobileView = false) => (
    <Dropdown
      renderToggle={({ onClick }) => (
        <button
          type="button"
          onClick={onClick}
          className={`btn btn-default ${
            isMobileView ? "mt-4 d-flex align-items-center" : ""
          }`}
        >
          <i className="bi bi-share me-2"></i>
          <span>Chia sẻ</span>
        </button>
      )}
      placement="bottom-start"
      renderMenu={() => (
        <div className="dropdown-menu">
          <a className="dropdown-item fs-14 px-3 py-2" onClick={handleCopyLink}>
            <i className="bi bi-clipboard2 me-2"></i> Sao chép đường dẫn
          </a>
          <Dropdown
            className="w-100"
            renderToggle={({ onClick }) => (
              <a
                className="dropdown-item fs-14 px-3 py-2 d-flex justify-content-between align-items-center"
                onClick={onClick}
                style={{ cursor: "pointer" }}
              >
                <span>
                  <i className="bi bi-qr-code me-2"></i> Quét mã QR
                </span>
              </a>
            )}
            placement="bottom-start"
            renderMenu={() => (
              <div className="dropdown-menu" ref={qrContainerRef}>
                <div className="d-flex flex-column align-items-center">
                  <QRCodeCanvas
                    value={window.location.href}
                    size={200}
                    title={quiz?.title || "Mã QR chia sẻ"}
                    includeMargin
                  />
                  <QrDownload qrContainerRef={qrContainerRef} />
                </div>
              </div>
            )}
          />
          <Dropdown
            className="w-100"
            renderToggle={({ onClick }) => (
              <a
                className="dropdown-item fs-14 px-3 py-2 d-flex justify-content-between align-items-center"
                onClick={onClick}
                style={{ cursor: "pointer" }}
              >
                <span>
                  <i className="bi bi-arrow-down-square me-2"></i> Tải về
                </span>
              </a>
            )}
            placement="bottom-start"
            renderMenu={() => (
              <div className="dropdown-menu" ref={qrContainerRef}>
                <div className="d-flex flex-column align-items-start">
                  <button className="btn btn-link btn-download-quiz text-muted" onClick={handleOpenPrintModal}>
                    <i className="bi bi-file-earmark-easel-fill"></i> In đề [ pdf ]
                  </button>
                  <button className="btn btn-link btn-download-quiz text-muted" onClick={() => downloadQuiz('quiz')}>
                    <i className="bi bi-file-earmark-arrow-down-fill"></i> Tải đề thi [docx]
                  </button>
                  <button className="btn btn-link btn-download-quiz text-muted" onClick={() => downloadQuiz('answer')}>
                    <i className="bi bi-file-earmark-richtext-fill"></i> Tải đáp án [docx]
                  </button>
                  <button className="btn btn-link btn-download-quiz text-muted" onClick={() => downloadQuiz('full')}>
                    <i className="bi bi-file-earmark-medical-fill"></i> Tải đề thi kèm đáp án [docx]
                  </button>
                </div>
              </div>
            )}
          />
        </div>
      )}
    />
  );

  const renderOtherBtnGroup = () => (
    <>
      {isOwner && (
        <Link
          href={`/dashboard/quiz/${quiz.id}/edit`}
          className="btn btn-default"
        >
          <i className="bi bi-pencil-square me-2"></i>
          <span>Chỉnh sửa</span>
        </Link>
      )}
      {user && !isOwner && (
        <button
          onClick={handleCopyQuiz}
          disabled={isLoading}
          className="btn btn-default"
        >
          {isLoading ? (
            <CircularProgress size={20} color="inherit" className="me-2" />
          ) : (
            <i className="bi bi-pencil-square me-2"></i>
          )}
          <span>Sao chép và chỉnh sửa</span>
        </button>
      )}
      <button onClick={handleSaveQuiz} className="btn btn-default">
        <i className="bi bi-folder me-2"></i>
        <span>Lưu</span>
      </button>
      {!isMobile && isTablet && !isStickyActive &&(
        renderShareDropdown()
      )}

      {!isMobile && isStickyActive && renderShareDropdown()}
    </>
  );

  return (
    <>
      <Card className="p-3">
        <div className="d-flex flex-md-row flex-column gap-3 mb-sm-3">
          <CardMedia className="quiz-banner show m-auto bg-body-secondary rounded-3">
            <NextImage
              imgStyle={{
                objectFit: "contain",
                objectPosition: "center",
              }}
              className="h-100"
              src={quiz.banner}
              alt={quiz.title}
            />
            {isEditable && (
              <Button
                className="btn-small"
                variant="contained"
                color="secondary"
                aria-label="edit"
                onClick={handleOpenDialogCropImage}
                size="small"
              >
                <EditIcon fontSize="small" />
              </Button>
            )}
          </CardMedia>
          <CardContent
            sx={{ flex: "1", overflow: "hidden" }}
            className="text-start p-1 p-md-3"
          >
            <div className="d-flex justify-content-between align-items-center mb-1">
            <div>
              <span className="badge bg-light text-dark">{quiz.type_text}</span>
              {quiz?.status === 0 && (
                <span className="h6 fs-13 text-danger" style={{ cursor: 'pointer'}} onClick={() => router.push(`/dashboard/quiz/${quiz.id}/edit`)}> [ Bản nháp ]</span>
              )}
              </div>
              {isEditable && (
                <Stack direction="row" sx={{ alignItems: "center", gap: 1 }}>
                  <button
                    className="btn btn-sm btn-icon text-danger"
                    onClick={deleteQuiz}
                  >
                    <i className="bi bi-trash3" />
                  </button>
                  <button
                    className="btn btn-sm btn-icon text-dark"
                    onClick={handleOpenDialogQuizFrom}
                    aria-hidden={false}
                  >
                    <i className="bi bi-gear-fill"></i>
                  </button>
                </Stack>
              )}
            </div>
            <div className="d-flex mt-2">
              <div className="w-100">
                <div className="position-relative mb-1">
                  <div
                    className="quiz-title"
                    style={{
                      minHeight: "47px",
                      maxWidth: "calc(100% - 50px)",
                      overflow: "hidden",
                    }}
                  >
                    {isEditable && !matchDownMD ? (
                      <EditableText
                        key={quiz.title}
                        value={quiz.title || ""}
                        emptyInit={true}
                        onFocus={(value) => console.log("on focus: ", value)}
                        onBlur={(value) => {
                          console.log("on blur: ", value);
                          editQuiz({ title: value });
                        }}
                        style={{
                          wordBreak: "break-word",
                        }}
                        textVariant="h1"
                        className="h4 fw-bold"
                      />
                    ) : (
                      <h1
                        className="h4 fw-bold"
                        style={{
                          wordBreak: "break-word",
                        }}
                      >
                        {quiz.title}
                      </h1>
                    )}
                  </div>
                </div>
                <div className="d-flex align-items-center flex-wrap align-content-stretch gap-1">
                  {quiz.editor && (
                    <span className="d-flex gap-2 align-items-center me-3">
                      <Avatar sx={{ width: 30, height: 30 }}>
                        {quiz.editor.name.charAt(0)}
                      </Avatar>
                      <span>{quiz.editor.name}</span>
                    </span>
                  )}
                  <span className="text-black-50 fs-14 me-3">
                    <i className="bi bi-patch-question me-1"></i>
                    {quiz.questions_count || quiz.questions?.length || 0} câu
                    hỏi
                  </span>
                  {quiz.subject && (
                    <span className="text-black-50 fs-14 me-3">
                      <i className="bi bi-file-earmark-medical me-1"></i>
                      {quiz.subject.title}
                    </span>
                  )}
                  {quiz.grade && (
                    <span className="text-black-50 fs-14 me-3">
                      <i className="bi bi-mortarboard-fill me-1"></i>
                      {quiz.grade.title}
                    </span>
                  )}
                </div>
              </div>
              {mounted && !isMobile && !isTablet && (
                <div className="box-shareq d-flex flex-column align-items-center text-center flex-shrink-0">
                  <div className="box-shareq-body" style={{ marginLeft: 'auto' }}>
                  <p className="q-separato mb-2 text-muted fs-15 w-100">
                    <span><i className="bi bi-reply me-1"></i>Chia sẻ đề thi</span>
                  </p>
                    <div className="bgroup-action d-flex gap-2 w-100">
                      <button
                        className="btn btn-sm bg-linear-copy text-white border-0 custom-style-shareq"
                        onClick={handleCopyLink}
                      >
                        <i className="bi bi-copy me-1"></i> Copy link
                      </button>
                      <Dropdown
                        renderToggle={({ onClick }) => (
                          <button
                            className="btn btn-sm w-100 bg-linear-copy text-white border-0 custom-style-shareq"
                            onClick={onClick}
                            style={{ cursor: "pointer" }}
                          >
                            <span>
                              <i className="bi bi-qr-code me-1"></i> Quét QR
                            </span>
                          </button>
                        )}
                        placement="bottom-start"
                        renderMenu={() => (
                          <div className="dropdown-menu" ref={qrContainerRef}>
                            <div className="d-flex flex-column justify-content-center align-items-center">
                              {typeof window !== "undefined" && (
                                <QRCodeCanvas
                                  value={window.location.href}
                                  size={200}
                                  title={quiz?.title || "Mã QR chia sẻ"}
                                  includeMargin
                                />
                              )}
                              <QrDownload qrContainerRef={qrContainerRef} />
                            </div>
                          </div>
                        )}
                      />
                    </div>
                    <p className="q-separato mt-2 mb-2 text-muted fs-15 w-100">
                      <span>hoặc tải đề</span>
                    </p>
                    <Dropdown
                      className="w-100"
                      renderToggle={({ onClick }) => (
                        <button
                          className="btn btn-sm flex-grow-1 text-nowrap text-white fw-medium border-0 w-100 custom-style-shareq"
                          onClick={onClick}
                          style={{ backgroundColor: "#009688", cursor: "pointer" }}
                        >
                          <span>
                          <i className="bi bi-download me-1"></i> In đề / Tải về
                          </span>
                        </button>
                      )}
                      placement="bottom-start"
                      renderMenu={() => (
                        <div className="dropdown-menu" ref={qrContainerRef}>
                          <div className="d-flex flex-column align-items-start">
                            <button className="btn btn-link btn-download-quiz text-muted" onClick={handleOpenPrintModal}>
                              <i className="bi bi-file-earmark-easel-fill"></i> In đề [ pdf ]
                            </button>
                            <button className="btn btn-link btn-download-quiz text-muted" onClick={() => downloadQuiz('quiz')}>
                              <i className="bi bi-file-earmark-arrow-down-fill"></i> Tải đề thi [docx]
                            </button>
                            <button className="btn btn-link btn-download-quiz text-muted" onClick={() => downloadQuiz('answer')}>
                              <i className="bi bi-file-earmark-richtext-fill"></i> Tải đáp án [docx]
                            </button>
                            <button className="btn btn-link btn-download-quiz text-muted" onClick={() => downloadQuiz('full')}>
                              <i className="bi bi-file-earmark-medical-fill"></i> Tải đề thi kèm đáp án [docx]
                            </button>
                          </div>
                        </div>
                      )}
                      />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </div>
      </Card>
      <>
        {isMobile && renderShareDropdown(true)}
        {!isMobile && <div ref={sentinelRef} style={{ height: 1 }} />}
        <div style={stickyStyle}>
          <Container maxWidth="cxl">
          <div style={{ overflowX: "auto", paddingBottom: '10px' }}>
            <div className="d-flex align-items-center gap-2 flex-nowrap" style={{ minWidth: "max-content" }}>
              {isMobile ? (
                <SwipeableDrawer
                  anchor="bottom"
                  open={openDrawer}
                  onClose={() => setOpenDrawer(false)}
                  onOpen={() => setOpenDrawer(true)}
                  disableSwipeToOpen
                  PaperProps={{
                    sx: {
                      borderTopLeftRadius: 12,
                      borderTopRightRadius: 12,
                      px: 2,
                      pb: 3,
                    },
                  }}
                >
                  <Box sx={{ textAlign: "center", py: 1 }}>
                    <div className="d-flex justify-content-center mb-3">
                      <div
                        style={{
                          width: 30,
                          height: 4,
                          background: "#ccc",
                          borderRadius: 2,
                        }}
                      />
                    </div>
                    <div className="d-flex flex-column gap-2">
                      {renderOtherBtnGroup()}
                      {renderPrimaryBtnGroup()}
                    </div>
                  </Box>
                </SwipeableDrawer>
              ) : (
                renderOtherBtnGroup()
              )}
              <div
                className={`${
                  isMobile ? "w-100" : "ms-auto"
                } d-flex justify-content-center align-items-center gap-2`}
              >
                {renderPrimaryBtnGroup()}
              </div>
              {isMobile && (
                <button
                  className="btn btn-default ms-auto"
                  onClick={() => setOpenDrawer(true)}
                >
                  <i className="bi bi-three-dots" />
                </button>
              )}
            </div>
          </div>
          </Container>
        </div>
      </>
      <DialogQuizForm
        quiz={quiz}
        open={openDialogQuizFrom}
        onClose={handleCloseDialogQuizFrom}
      />
      {openPrintModal && (
        <QuizPreviewModal
          open={openPrintModal}
          onClose={handleClosePrintModal}
          print={true}
          quiz={quiz}
        />
      )}
      {openSaveModal && (
        <SaveToCollectionModal
          open={openSaveModal}
          handleClose={handleCloseSaveModal}
          quiz={quiz}
        />
      )}
      {openDialogCropImage && (
        <DialogDragDropCropImage
          open={openDialogCropImage}
          onClose={handleCloseDialogCropImage}
          imgSrc={quiz.banner}
          setImgSrc={(value) => {
            if (value) {
              if (isBase64(value)) {
                editQuiz({ banner_base64: value }, handleCloseDialogCropImage);
              } else {
                handleCloseDialogCropImage();
              }
            } else {
              editQuiz({ remove_banner: 1 });
            }
          }}
        />
      )}
    </>
  );
};

export default ActionButtons;
