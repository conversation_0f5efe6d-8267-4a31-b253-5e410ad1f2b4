import React, { useState, useCallback, useMemo } from 'react';

import Dialog from '@mui/material/Dialog';
import DialogTitle from '@mui/material/DialogTitle';
import DialogContent from '@mui/material/DialogContent';
import IconButton from '@mui/material/IconButton';
import CloseIcon from '@mui/icons-material/Close';

import { useRouter } from 'next/navigation';

// third party
import { useTranslations } from "next-intl";
import { useSearchParams } from "next/navigation";

import { questionTypes } from "@/constant";
import toast from "react-hot-toast";
import { updateQuiz } from '@/actions/quizAction';

const QuestionTypeDialog = ({ createQuestion, quiz }) => {
  console.log('QuestionTypeDialog');
  const t = useTranslations("Common");
  const homework = useSearchParams().get("homework");
  const router = useRouter();
  const [open, setOpen] = useState(false);
  const [publish, setPublish] = useState(quiz?.status === 1);

  const handleCloseDialog = useCallback(() => {
    setOpen(false);
  }, []);

  const handleOpenDialog = useCallback(() => {
    setOpen(true);
  }, []);

  const handleQuestionDialog = useCallback((initData) => {
    setOpen(false);
    createQuestion(initData);
  }, [createQuestion]);

  const handlePublish = useCallback((quiz) => async () => {
    if (!quiz) return;

    if (questionCount <= 0) {
      toast.error("Chưa có câu hỏi nào trong quiz này. Vui lòng thêm câu hỏi trước khi xuất bản.");
    } else {
      try {
        const response = await updateQuiz({
          id: quiz.id,
          title: quiz.title,
          status: 1
        });
        if (response && response.data) {
          setPublish(pre => !pre);
        }
        toast.success('thành công');
      } catch (error) {
        console.error("Error publishing quiz:", error);
        toast.error("Có lỗi xảy ra khi xuất bản quiz.");
      }
    }
  }, [router]);

  const questionCount = useMemo(
    () => quiz?.questions_count || quiz?.questions?.length || 0,
    [quiz]
  );

  return (
    <>
      {!homework && questionCount > 0 && (
        <button className="btn btn-outline-primary2 btn-sm ms-3" onClick={() => router.push(`/dashboard/quiz/${quiz.id}/homework`)}>
          <i className="bi bi-send me-2"></i>
          <strong>Giao bài</strong>
        </button>
      )}
      <button className="btn btn-primary2 btn-sm" onClick={handleOpenDialog}>
        <i className="bi bi-plus-circle me-2"></i>
        <strong>Thêm câu hỏi</strong>
      </button>
      {publish ? (
        <button className="btn btn-danger btn-sm" onClick={handlePublish(quiz)}>
          <i className="bi bi-check-square me-2"></i>
          <strong>Ngừng xuất bản</strong>
        </button>
        ) : (
          <button className="btn btn-primary3 btn-sm" onClick={handlePublish(quiz)}>
            <i className="bi bi-square me-2"></i>
            <strong>Xuất bản</strong>
          </button>
      )}
      <Dialog
        fullWidth
        maxWidth={'sm'}
        open={open}
        onClose={handleCloseDialog}
        aria-labelledby="alert-dialog-title"
        aria-describedby="alert-dialog-description"
        sx={{
          '& .MuiDialog-paper': {
            backgroundColor: '#E0E0E0',
          },
        }}
      >
        <IconButton
          aria-label="close"
          onClick={handleCloseDialog}
          sx={{
            position: 'absolute',
            right: 0,
            top: 0,
            color: (theme) => theme.palette.grey[500],
          }}
        >
          <CloseIcon />
        </IconButton>
        <DialogTitle className="h5" id="alert-dialog-title">
          Lựa chọn dạng câu hỏi:
        </DialogTitle>
        <DialogContent>
          <div className="btn-group">
            {Object.entries(questionTypes).map(([keyType, questionType]) => {
              return (
                <button
                  key={keyType}
                  className="btn btn-icon-link"
                  onClick={() => handleQuestionDialog({ type: keyType, content_json: questionType.initQue })}
                >
                  { questionType.icon && (<span className="me-2 icon-link" dangerouslySetInnerHTML={{ __html: questionType.icon }} />) }
                  { t(keyType) }
                </button>
                );
            })}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}

export default React.memo(QuestionTypeDialog);
