"use client";

import { memo, useMemo, useState, useCallback } from "react";
import { useDispatch } from "react-redux";
import { useRouter } from "next/navigation";
import Link from "next/link";

import { styled } from "@mui/material/styles";
import { purple } from "@mui/material/colors";
import Paper from "@mui/material/Paper";
import Stack from "@mui/material/Stack";
import InputBase from "@mui/material/InputBase";
import Skeleton from "@mui/material/Skeleton";
import Card from "@mui/material/Card";
import CardContent from "@mui/material/CardContent";
import CardActionArea from "@mui/material/CardActionArea";
import Button from "@mui/material/Button";
import IconButton from "@mui/material/IconButton";
import Accordion from "@mui/material/Accordion";
import AccordionDetails from "@mui/material/AccordionDetails";
import AccordionSummary from "@mui/material/AccordionSummary";

import SearchIcon from "@mui/icons-material/Search";
import ExpandMoreIcon from "@mui/icons-material/ExpandMore";
import MoreVertIcon from "@mui/icons-material/MoreVert";

import Dropdown from "@/components/Dropdown";
import NoDataOverlay from "@/components/NoDataOverlay";
import DialogAssignmentForm from "../DialogAssignmentForm";

import toast from "react-hot-toast";
import debounce from "lodash/debounce";
import groupBy from "lodash/groupBy";

import { useConfirm } from "@/contexts/ConfirmContext";

import { classroomApiSlice } from "@/slices/features/classroomApiSlice";
import { removeAssignment } from "@/actions/classroomAction";
import { getColorByPercentage, formatDateOfWeekFromTs } from "@/utils/helpers";

const GroupInputSearch = styled(Paper)(() => ({
  padding: "2px 4px",
  display: "flex",
  alignItems: "center",
  border: "1px solid #e5e5e5",
  width: "100%",
  height: "39px",
  maxWidth: "256px",
  borderRadius: "8px",
}));

const CustomAccordion = styled(Accordion)(() => ({
  background: "transparent",
  ".MuiAccordionSummary-root": {
    justifyContent: "start",
    minHeight: "30px !important",
    gap: "8px",
  },
  ".MuiAccordionSummary-content": {
    flexGrow: "inherit",
    margin: "8px 0 !important",
  },
}));

const ColorButton = styled(Button)(({ theme }) => ({
  color: theme.palette.getContrastText(purple[500]),
  backgroundColor: purple[700],
  "&:hover": {
    backgroundColor: purple[500],
  },
}));

const AssignmentTab = ({ classroom }) => {
  console.log('AssignmentTab');
  const router = useRouter();
  const dispatch = useDispatch();
  const confirmDelete = useConfirm();

  const [currentAssignment, setCurrentAssignment] = useState({});
  const [openDialogAssignmentForm, setOpenDialogAssignmentForm] = useState(false);
  const [tabValueAssignmentForm, setTabValueAssignmentForm] = useState(0);
  const [keyword, setKeyword] = useState("");

  const { data: assignments, isLoading } = classroomApiSlice.useFetchAssignmentsQuery({
    classroomId: classroom.id,
    params: { keyword },
  });

  const debouncedSearch = useCallback(
    debounce((value) => {
      setKeyword(value);
    }, 700),
    []
  );

  const handleSearch = useCallback((event) => {
    debouncedSearch(event.target.value);
  }, []);

  const addAssignment = useCallback(() => {
    router.push(`/dashboard/quiz/create?classroom=${classroom.id}`);
  }, []);

  const editAssignment = useCallback((assignment, tabValue) => {
    setCurrentAssignment(assignment);
    setTabValueAssignmentForm(tabValue);
    setOpenDialogAssignmentForm(true);
  }, []);

  const deleteAssignment = useCallback(
    async (assignment) => {
      const confirm = await confirmDelete(
        `Xóa: ${assignment.subtitle || assignment.title}?`,
        "Sau khi xóa, bài tập sẽ không còn xuất hiện!"
      );

      if (confirm) {
        try {
          await removeAssignment(classroom.id, assignment.id);

          dispatch(
            classroomApiSlice.util.invalidateTags([{ type: "Classroom", id: `ASSIGNMENTS-${classroom.id}` }])
          );

          toast.success("Xóa thành công!", {
            position: "top-right",
          });
        } catch (error) {
          console.error(error);
          toast.error("Xóa lớp học thất bại");
        }
      }
    },
    [classroom.id, confirmDelete]
  );

  const submitAssignment = useCallback((classroomIds = []) => {
    if (classroomIds.length > 0) {
      classroomIds.forEach((classroomId) => {
        dispatch(
          classroomApiSlice.util.invalidateTags([{ type: "Classroom", id: `ASSIGNMENTS-${classroomId}` }])
        );
      });
    }

    toast.success('Cập nhật bài tập thành công.', {
      position: "top-right",
    });

    setOpenDialogAssignmentForm(false);
  }, [dispatch]);

  const groupedAssignmentsMemo = useMemo(() => {
    return assignments ? groupBy(assignments, "start_day") : {};
  }, [assignments]);

  const GroupedAssignments = memo(
    ({ groupedAssignments, onUpdate, onDelete }) => {
      console.log("GroupedAssignments");

      return Object.keys(groupedAssignments).map((start_day, index) => (
        <div className="mb-4" key={index}>
          <CustomAccordion className="border-0" defaultExpanded>
            <AccordionSummary
              expandIcon={<ExpandMoreIcon />}
              aria-controls={`panel${index}-content`}
              id={`panel${index}-header`}
              className="px-0 fs-16"
            >
              <strong>
                {start_day > 0
                  ? formatDateOfWeekFromTs(start_day)
                  : "Bài tập không có thời hạn"}
              </strong>
              :
              <span className="ms-2">
                ({groupedAssignments[start_day].length} bài tập)
              </span>
            </AccordionSummary>
            <AccordionDetails className="p-0">
              <div className="row">
                {groupedAssignments[start_day].map((item) => (
                  <div className="col-12" key={item.id}>
                    <Card className="p-1 mb-3">
                      <CardContent className="p-0">
                        <Stack
                          spacing={1}
                          direction="row"
                          sx={{
                            justifyContent: "space-between",
                            alignItems: "center",
                          }}
                        >
                          <CardActionArea
                            className="p-3"
                            component={Link}
                            href={`/dashboard/reports/${item.id}`}
                          >
                            <h4 className="fs-18 fw-normal">
                              {item.subtitle || item.title}
                            </h4>
                            <div className="mt-3 d-flex flex-wrap justify-content-between align-items-center">
                              <span className="text-black-50 fs-14">
                                <i className="bi bi-calendar-check me-1 text-primary"></i>
                                Số học sinh đã làm:
                                <strong className="ms-2">
                                  {item.quiz_results_count || 0}/
                                  {classroom.classroom_user_count || 0}
                                </strong>
                              </span>
                              <span className="text-black-50 fs-14">
                                <i className="bi bi-check-circle me-1" style={{ color: "#4caf50" }}></i>
                                Tỉ lệ câu đúng:
                                <strong className="ms-2" style={{
                                  color: getColorByPercentage(item.correct_percentage)
                                }}>
                                  {item.correct_percentage || 0}%
                                </strong>
                              </span>
                              <span className="text-black-50 fs-14">
                                <i className="bi bi-gear me-1"></i>
                                Xem báo cáo chi tiết <i className="bi bi-arrow-right-short ms-1"></i>
                              </span>
                            </div>
                          </CardActionArea>
                          <div className="mx-md-3">
                            <Dropdown
                              renderToggle={({ onClick }) => (
                                <button
                                  type="button"
                                  onClick={onClick}
                                  className="btn btn-link btn-dropdown-small dropdown-toggle"
                                >
                                  <MoreVertIcon />
                                </button>
                              )}
                              placement="bottom-end"
                              renderMenu={() => (
                                <div className="dropdown-menu">
                                  <a
                                    onClick={() => onUpdate(item, 0)}
                                    className="dropdown-item fs-14"
                                  >
                                    <i className="bi bi-gear me-2"></i>
                                    Cài đặt
                                  </a>
                                  <a
                                    onClick={() => onUpdate(item, 1)}
                                    className="dropdown-item fs-14"
                                  >
                                    <i className="bi bi-send-plus me-2"></i>
                                    Giao thêm cho lớp
                                  </a>
                                  <a
                                    onClick={() => router.push(`/dashboard/quiz/${item.quiz_id}/edit`)}
                                    className="dropdown-item fs-14"
                                  >
                                    <i className="bi bi-pencil-square me-2"></i>
                                    Chỉnh sửa câu hỏi
                                  </a>
                                  <hr className="dropdown-divider" />
                                  <a
                                    onClick={() => onDelete(item)}
                                    className="dropdown-item fs-14"
                                  >
                                    <i className="bi bi-trash me-2"></i>
                                    Xóa
                                  </a>
                                </div>
                              )}
                            />
                          </div>
                        </Stack>
                      </CardContent>
                    </Card>
                  </div>
                ))}
              </div>
            </AccordionDetails>
          </CustomAccordion>
        </div>
      ));
    }
  );

  GroupedAssignments.displayName = "GroupedAssignments";

  return (
    <>
      <p className="fs-18">Danh sách các bài tập đã giao:</p>
      <div className="d-flex flex-wrap align-items-center justify-content-between gap-2">
        <GroupInputSearch>
          <IconButton
            size="small"
            color="inherit"
            aria-label="search"
            sx={{ minWidth: "20px" }}
            disabled
          >
            <SearchIcon />
          </IconButton>
          <InputBase
            placeholder="Tìm bài tập"
            inputProps={{ "aria-label": "Tìm bài tập" }}
            onChange={handleSearch}
          />
        </GroupInputSearch>
        <ColorButton onClick={addAssignment}>
          <i className="bi bi-plus-circle me-2"></i>
          Thêm bài tập cho lớp
        </ColorButton>
      </div>
      <div className="mt-4">
        {isLoading ? (
          Array.from({ length: 3 }, (_, index) => (
            <Skeleton
              variant="rounded"
              height={70}
              key={index}
              sx={{ my: "10px", borderRadius: "4px" }}
            />
          ))
        ) : assignments?.length ? (
          <>
            <GroupedAssignments
              groupedAssignments={groupedAssignmentsMemo}
              onUpdate={editAssignment}
              onDelete={deleteAssignment}
            />
          </>
        ) : (
          <NoDataOverlay message="Không có bài tập nào!" />
        )}
      </div>
      {openDialogAssignmentForm && (
        <DialogAssignmentForm
          classroom={classroom}
          currentAssignment={currentAssignment}
          submitAssignment={submitAssignment}
          open={openDialogAssignmentForm}
          defaultTabValue={tabValueAssignmentForm}
          onClose={() => setOpenDialogAssignmentForm(false)}
        />
      )}
    </>
  );
};

export default memo(AssignmentTab);
