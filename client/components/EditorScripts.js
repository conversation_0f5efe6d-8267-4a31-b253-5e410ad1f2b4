"use client";

import Script from "next/script";
import { useEffect } from "react";

export default function EditorScripts() {
  const editorDomain = process.env.NEXT_PUBLIC_EDITOR_DOMAIN;

  useEffect(() => {
    if (typeof window !== "undefined") {
      window.EDITOR_DOMAIN = editorDomain;
      window._is_mobile = false;
      window._is_agent_phone = false;
    }
  }, [editorDomain]);

  return (
    <>
      <Script
        src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js"
        strategy="afterInteractive"
        onLoad={() => console.log("✅ jQuery ready")}
      />
      <Script
        src="https://cdn.jsdelivr.net/npm/compressorjs@1.2.1/dist/compressor.min.js"
        strategy="afterInteractive"
        onLoad={() => console.log("✅ Compressor ready")}
      />
      <Script
        src={`${editorDomain}ckeditor4/ckeditor.js?id=1`}
        strategy="afterInteractive"
        onLoad={() => console.log("✅ CKEditor loaded")}
      />
      <Script
        src={`${editorDomain}ckeditor4/plugins/ckeditor_wiris/integration/WIRISplugins.js?viewer=image`}
        strategy="afterInteractive"
        onLoad={() => console.log("✅ WIRIS loaded")}
      />
    </>
  );
}
