"use client";

import React, { useState, useRef } from 'react';
import { 
  <PERSON><PERSON>, 
  <PERSON><PERSON>Title, 
  <PERSON>alogContent, 
  <PERSON>alogActions, 
  Button, 
  Typography, 
  Box,
  Alert,
  Chip
} from '@mui/material';
import CKEditor from '@/components/CKEditor';

/**
 * Test component for CKEditor LaTeX/Math formula editing functionality
 * This component helps verify that the LaTeX editing issues have been resolved
 */
const CKEditorMathTest = ({ open, onClose }) => {
  const [editorContent, setEditorContent] = useState(`
    <p>Test LaTeX formulas for editing:</p>
    <p>1. Inline formula: \\(\\frac{x}{5} + \\frac{y}{4} + \\frac{z}{3} = 0\\)</p>
    <p>2. Display formula: $$E = mc^2$$</p>
    <p>3. Complex formula: \\(\\int_{-\\infty}^{\\infty} e^{-x^2} dx = \\sqrt{\\pi}\\)</p>
    <p>4. Matrix: \\(\\begin{pmatrix} a & b \\\\ c & d \\end{pmatrix}\\)</p>
    <p><strong>Instructions:</strong> Double-click on any formula above to edit it.</p>
  `);
  
  const [testResults, setTestResults] = useState([]);
  const editorRef = useRef(null);

  const handleEditorReady = (editor) => {
    editorRef.current = editor;
    console.log('Test Editor Ready:', editor);
    
    // Test if math plugins are loaded
    const mathPluginLoaded = editor.plugins.mathjax || editor.plugins.ckeditor_wiris;
    const wirisAvailable = !!window.WirisPlugin;
    const mathJaxAvailable = !!window.MathJax;
    
    setTestResults([
      {
        test: 'Math Plugin Loaded',
        status: mathPluginLoaded ? 'PASS' : 'FAIL',
        details: mathPluginLoaded ? 'Math plugin is available' : 'Math plugin not found'
      },
      {
        test: 'WIRIS Plugin Available',
        status: wirisAvailable ? 'PASS' : 'FAIL',
        details: wirisAvailable ? 'WIRIS plugin is loaded' : 'WIRIS plugin not available'
      },
      {
        test: 'MathJax Available',
        status: mathJaxAvailable ? 'PASS' : 'FAIL',
        details: mathJaxAvailable ? 'MathJax library is loaded' : 'MathJax not available'
      }
    ]);

    // Test double-click functionality
    editor.on('doubleclick', (evt) => {
      const element = evt.data.element;
      if (element && (element.hasClass('math-tex') || element.is('math'))) {
        console.log('Math formula double-clicked:', element);
        setTestResults(prev => [...prev, {
          test: 'Double-click Detection',
          status: 'PASS',
          details: 'Math formula double-click detected successfully'
        }]);
      }
    });
  };

  const handleEditorChange = (event) => {
    if (editorRef.current) {
      const data = editorRef.current.getData();
      setEditorContent(data);
    }
  };

  const insertTestFormula = () => {
    if (editorRef.current) {
      const testFormula = '\\(\\sum_{i=1}^{n} x_i = \\frac{n(n+1)}{2}\\)';
      editorRef.current.insertHtml(`<span class="math-tex">${testFormula}</span>`);
      
      setTestResults(prev => [...prev, {
        test: 'Formula Insertion',
        status: 'PASS',
        details: 'Test formula inserted successfully'
      }]);
    }
  };

  const testMathJaxRendering = () => {
    if (window.MathJax && window.MathJax.Hub) {
      window.MathJax.Hub.Queue(["Typeset", window.MathJax.Hub]);
      setTestResults(prev => [...prev, {
        test: 'MathJax Rendering',
        status: 'PASS',
        details: 'MathJax re-rendering triggered'
      }]);
    } else {
      setTestResults(prev => [...prev, {
        test: 'MathJax Rendering',
        status: 'FAIL',
        details: 'MathJax not available for rendering'
      }]);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      disableEnforceFocus
      disableAutoFocus
    >
      <DialogTitle>
        CKEditor LaTeX/Math Formula Test
      </DialogTitle>
      
      <DialogContent>
        <Box mb={2}>
          <Alert severity="info">
            <Typography variant="body2">
              This test verifies that LaTeX formula editing functionality works correctly.
              Try double-clicking on the math formulas in the editor below to test editing capabilities.
            </Typography>
          </Alert>
        </Box>

        <Box mb={2}>
          <Typography variant="h6" gutterBottom>
            Test Results:
          </Typography>
          <Box display="flex" flexWrap="wrap" gap={1} mb={2}>
            {testResults.map((result, index) => (
              <Chip
                key={index}
                label={`${result.test}: ${result.status}`}
                color={result.status === 'PASS' ? 'success' : 'error'}
                variant="outlined"
                size="small"
              />
            ))}
          </Box>
          
          <Box display="flex" gap={1} mb={2}>
            <Button 
              variant="outlined" 
              size="small" 
              onClick={insertTestFormula}
            >
              Insert Test Formula
            </Button>
            <Button 
              variant="outlined" 
              size="small" 
              onClick={testMathJaxRendering}
            >
              Test MathJax Rendering
            </Button>
            <Button 
              variant="outlined" 
              size="small" 
              onClick={clearResults}
            >
              Clear Results
            </Button>
          </Box>
        </Box>

        <Box mb={2}>
          <Typography variant="h6" gutterBottom>
            CKEditor with Math Support:
          </Typography>
          <CKEditor
            config={{
              height: 400,
              toolbar: "EditorQuiz",
              allowedContent: true,
              extraAllowedContent: '*[*]{*}(*)',
              readOnly: false,
            }}
            initData={editorContent}
            onInstanceReady={handleEditorReady}
            onChange={handleEditorChange}
          />
        </Box>

        <Box>
          <Typography variant="h6" gutterBottom>
            Test Details:
          </Typography>
          {testResults.map((result, index) => (
            <Alert 
              key={index} 
              severity={result.status === 'PASS' ? 'success' : 'error'}
              sx={{ mb: 1 }}
            >
              <strong>{result.test}:</strong> {result.details}
            </Alert>
          ))}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={onClose}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default CKEditorMathTest;
