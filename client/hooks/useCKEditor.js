import React, { useEffect, useRef, useState } from 'react';

const defEditorUrl = 'https://cdn.ckeditor.com/4.24.0-lts/standard-all/ckeditor.js';
const defConfig = {};

// Utility function to load the CKEditor script and get the CKEDITOR namespace
const getEditorNamespace = (editorUrl) => {
    return new Promise((resolve, reject) => {
        if (window.CKEDITOR) {
            resolve(window.CKEDITOR);
            return;
        }

        console.log('Load the CKEditor script');

        const script = document.createElement('script');
        script.src = editorUrl;
        script.onload = () => {
            if (window.CKEDITOR) {
                resolve(window.CKEDITOR);
            } else {
                reject(new Error('CKEditor failed to load'));
            }
        };
        script.onerror = () => {
            reject(new Error('Failed to load CKEditor script'));
        };
        document.head.appendChild(script);
    });
};

export default function useCKEditor({ config, editorUrl, element, initContent, type = 'classic', onInstanceReady = () => {}, onChange = () => {} }) {
    console.log('useCKEditor');
    const editorUrlRef = useRef(editorUrl || defEditorUrl);
    const configRef = useRef(config || defConfig);
    const typeRef = useRef(type);
    const initContentRef = useRef(initContent);
    const [editor, setEditor] = useState(null);

    useEffect(() => {
        if (element) {
            getEditorNamespace(editorUrlRef.current)
                .then((CKEDITOR) => {
                    const isInline = typeRef.current === 'inline';
                    const newEditor = CKEDITOR[isInline ? 'inline' : 'replace'](element, configRef.current);

                    newEditor.on('instanceReady', () => {
                        console.log(WirisPlugin?.Parser?.initParse);
                        // if (initContentRef.current) {
                        //     newEditor.setData(initContentRef.current);
                        // }

                        if (typeof onInstanceReady === 'function') {
                            onInstanceReady(newEditor);
                        }
                    });

                    newEditor.on('change', (event) => {
                        if (typeof onChange === 'function') {
                            onChange(event);
                        }
                    });

                    setEditor(newEditor);
                })
                .catch((error) => {
                    console.error(error);
                });
        }

        return () => {
            if (editor) {
                editor.destroy();
            }
        };
    }, [element]);

    return editor;
}
