"use client";

import { useEffect, useRef, useMemo } from "react";
import { Provider } from "react-redux";
import { setupListeners } from "@reduxjs/toolkit/query";
import { usePathname } from "next/navigation";
import Script from 'next/script';

import { Toaster } from "react-hot-toast";

// project imports
import NavigationScroll from '@/themes/NavigationScroll';
// import { ConfigProvider } from '@/contexts/ConfigContext';
import ThemeCustomization from '@/themes';

import { ConfirmProvider } from '@/contexts/ConfirmContext';

import useMathJax from '@/hooks/useMathJax';

import { makeStore } from "@/store";

export const StoreProvider = ({ children }) => {
  const storeRef = useRef(null);

  if (!storeRef.current) {
    // Create the store instance the first time this renders
    storeRef.current = makeStore();
  }

  useEffect(() => {
    if (storeRef.current != null) {
      // Configure listeners using the provided defaults
      // Optional, but required for `refetchOnFocus`/`refetchOnReconnect` behaviors
      const unsubscribe = setupListeners(storeRef.current.dispatch);
      return unsubscribe;
    }
  }, []);

  return <Provider store={storeRef.current}>{children}</Provider>;
};

export const AppProvider = ({ children }) => {
  return (
      <ThemeCustomization>
        <NavigationScroll>
          <ConfirmProvider>
            {children}
            <Toaster />
          </ConfirmProvider>
        </NavigationScroll>
      </ThemeCustomization>
  );
};

export const ScripsClient = () => {
  const pathname = usePathname();
  // const editorDomain = process.env.NEXT_PUBLIC_EDITOR_DOMAIN;

  useMathJax([pathname]);

  return (
    <>
      {/*<Script id="editor-inline" strategy="beforeInteractive">
        {`
          var baseURL = EDITOR_DOMAIN = '${editorDomain}';
          var _is_mobile = false;
          var _is_agent_phone = false;
        `}
      </Script>
      <Script src="https://cdnjs.cloudflare.com/ajax/libs/jquery/3.7.1/jquery.min.js" />
      <Script src="https://cdn.jsdelivr.net/npm/compressorjs@1.2.1/dist/compressor.min.js" />
      <Script src={`${editorDomain}ckeditor4/plugins/ckeditor_wiris/integration/WIRISplugins.js?viewer=image`} />
      <Script src={`${editorDomain}ckeditor4/ckeditor.js`} />*/}
      <Script
        id="mathjax-js"
        src="https://cdnjs.cloudflare.com/ajax/libs/mathjax/2.7.2/MathJax.js?config=TeX-MML-AM_CHTML"
        strategy="lazyOnload"
        onLoad={() => {
          window.MathJax.Hub.Config({
            showProcessingMessages: false,
            messageStyle: "none",
            tex2jax: {
              inlineMath: [["$", "$"], ["\\(", "\\)"]],
              displayMath: [["$$", "$$"]],
              processEscapes: true,
            },
            "HTML-CSS": { scale: 350 },
          });
          window.MathJax.Hub.Queue(["Typeset", window.MathJax.Hub]);
        }}
      />
    </>
  );
};
