import MainLayout from "@/layout/MainLayout";
import { notFound } from "next/navigation";
import { cookies } from "next/headers";

import { fetchQuiz } from "@/actions/quizAction";
import QuizEdit from "@/components/quizzes/QuizEdit";
import InitNoti from "@/components/InitNoti";

import EditorScripts from "@/components/EditorScripts";

import { getDataUserFromCookie } from '@/app/actions';

export async function generateMetadata({ params }, parent) {
  const { id } = await params;
  const token = await getDataUserFromCookie('token');
  const quiz = await fetchQuiz(id, token);

  if (quiz?.status === 404) {
    return { title: "Quiz Not Found" };
  }

  return {
    title: quiz?.data?.title || "Something went wrong!",
    alternates: {
      canonical: `/dashboard/quiz/${id}/edit`,
    },
  };
}

export default async function Page({ params }) {
  console.log("Quiz Edit");
  const { id } = await params;
  const token = await getDataUserFromCookie('token');
  const quiz = await fetchQuiz(id, token);

  if (quiz?.status === 403 || quiz?.status === 404) {
    return notFound();
  }

  return (
    <MainLayout>
      <EditorScripts />
      { quiz?.data ? <QuizEdit initQuiz={quiz.data} /> : <InitNoti initValue={quiz} /> }
    </MainLayout>
  );
}
