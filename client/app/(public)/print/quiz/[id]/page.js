import React from 'react'
import QuizPreview from '@/components/quizzes/QuizPreview'
import { quizInfo } from '@/actions/quizAction'
import { notFound } from 'next/navigation'

export async function generateMetadata({ params }, parent) {
  const { id } = await params

  const { data: quiz } = await quizInfo(id);

  if (!quiz) {
    return { title: "Quiz Not Found" };
  }

  const title = `In đề: ${ quiz.title }`;

  return {
    title,
    alternates: {
      canonical: `/print/quiz/${quiz.id}`,
    },
    openGraph: {
      type: 'article',
      title,
    },
    twitter: {
      title,
    },
  };
}

export default async function PreviewPage({ params, searchParams }) {
  const { id } = await params
  const { print } = await searchParams;
  const { data: quiz } = await quizInfo(id);

  if (!quiz) {
    return notFound()
  }

  return { print === 'true' ? (<QuizPrint quiz={quiz} /> : <QuizPreview quiz={quiz} /> };

  <QuizPreview quiz={quiz} print={print === 'true'}/>
}
