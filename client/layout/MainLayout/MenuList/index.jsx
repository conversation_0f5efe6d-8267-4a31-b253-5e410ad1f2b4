import { memo, useState } from 'react';
import { useSelector } from "react-redux";

import Divider from '@mui/material/Divider';
import List from '@mui/material/List';
import Typography from '@mui/material/Typography';
import Box from '@mui/material/Box';

// project imports
import NavItem from './NavItem';
import NavGroup from './NavGroup';
import allMenuItems from '@/menu-items';

// ==============================|| SIDEBAR MENU LIST ||============================== //

function MenuList() {
  const { opened } = useSelector((state) => state.customization);
  const { user } = useSelector((state) => state.auth);

  const [selectedID, setSelectedID] = useState('');

  // Filter menu items based on user roles
  const filterMenuItems = (items) => {
    return items.map(item => {
      if (item.children) {
        const filteredChildren = item.children.filter(child => {
          if (child.roles && child.roles.length > 0) {
            // Check if user has any of the required roles
            return child.roles.includes(user?.role?.name);
          }
          return true;
        });
        return { ...item, children: filteredChildren };
      }
      return item;
    });
  };

  const menuItems = { ...allMenuItems, items: filterMenuItems(allMenuItems.items) };

  const lastItem = null;

  let lastItemIndex = menuItems.items.length - 1;
  let remItems = [];
  let lastItemId;

  if (lastItem && lastItem < menuItems.items.length) {
    lastItemId = menuItems.items[lastItem - 1].id;
    lastItemIndex = lastItem - 1;
    remItems = menuItems.items.slice(lastItem - 1, menuItems.items.length).map((item) => ({
      title: item.title,
      elements: item.children,
      icon: item.icon,
      ...(item.url && {
        url: item.url
      })
    }));
  }

  const navItems = menuItems.items.slice(0, lastItemIndex + 1).map((item, index) => {
    switch (item.type) {
      case 'group':
        if (item.url && item.id !== lastItemId) {
          return (
            <List key={item.id}>
              <NavItem item={item} level={1} isParents setSelectedID={() => setSelectedID('')} />
              {index !== 0 && <Divider sx={{ py: 0.5 }} />}
            </List>
          );
        }

        return (
          <NavGroup
            key={item.id}
            setSelectedID={setSelectedID}
            selectedID={selectedID}
            item={item}
            lastItem={lastItem}
            remItems={remItems}
            lastItemId={lastItemId}
          />
        );
      default:
        return (
          <Typography key={item.id} variant="h6" color="error" align="center">
            Menu Items Error
          </Typography>
        );
    }
  });

  return <Box {...(opened && { sx: { mt: 1.5 } })}>{navItems}</Box>;
}

export default memo(MenuList);
